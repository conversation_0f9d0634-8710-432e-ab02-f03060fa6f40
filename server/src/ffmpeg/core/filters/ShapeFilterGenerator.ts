import { CanvasState } from "../../types";
import { AbstractFilterGenerator } from "./BaseFilter";
import { ShapeImageGenerator } from "../generators/ShapeImageGenerator";
import logger from "../../../utils/logger";

/**
 * Filter generator for shape elements
 * Uses ImageMagick to generate precise shape images, then applies them as overlays
 */
export class ShapeFilterGenerator extends AbstractFilterGenerator {
  private shapeImageGenerator: ShapeImageGenerator;
  private tempImagePaths: string[] = [];

  constructor() {
    super();
    this.shapeImageGenerator = new ShapeImageGenerator();
  }

  /**
   * 生成shape元素的滤镜字符串（同步版本，用于兼容基类接口）
   */
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    startTime: number,
    duration: number,
    index: number
  ): string {
    // 这个方法保持同步，但实际的shape处理需要异步调用generateShapeFilter
    throw new Error(
      "Shape elements require async processing. Use generateShapeFilter instead."
    );
  }

  /**
   * 异步生成shape元素的滤镜字符串
   */
  async generateShapeFilter(
    element: CanvasState["elements"][0],
    index: number
  ): Promise<{ filterString: string; imageInputPath: string }> {
    try {
      const { placement, properties, timeFrame } = element;

      if (!placement) {
        throw new Error(
          `Shape element at index ${index} missing placement data`
        );
      }

      if (!properties) {
        throw new Error(
          `Shape element at index ${index} missing properties data`
        );
      }

      const {
        shapeType,
        fill = "#9e9e9e",
        stroke = "#757575",
        strokeWidth = 1,
        borderRadius = 0,
      } = properties as any;

      // 计算形状的位置和尺寸
      const x = Math.round(placement.x);
      const y = Math.round(placement.y);
      const width = Math.round(placement.width);
      const height = Math.round(placement.height);
      const rotation = placement.rotation || 0;

      logger.info(
        `生成${shapeType}形状滤镜，尺寸: ${width}x${height}, 位置: (${x}, ${y})`
      );

      // 使用ImageMagick生成shape图片
      const shapeImagePath = await this.shapeImageGenerator.generateShapeImage(
        shapeType,
        width,
        height,
        fill,
        stroke,
        strokeWidth,
        borderRadius,
        element.opacity || 1
      );

      // 记录临时文件路径，用于后续清理
      this.tempImagePaths.push(shapeImagePath);

      logger.info(`成功生成shape图片: ${shapeImagePath}`);

      // 生成图片叠加滤镜（类似ImageFilterGenerator的处理方式）
      // 计算实际的图片尺寸（包含边框空间和图片放大）
      const actualImageDimensions = this.calculateActualImageDimensions(
        width,
        height,
        strokeWidth
      );

      let filterString = this.generateImageOverlayFilter(
        x,
        y,
        width,
        height,
        actualImageDimensions.width,
        actualImageDimensions.height,
        rotation,
        timeFrame.start / 1000,
        timeFrame.end / 1000,
        index
      );

      return {
        filterString,
        imageInputPath: shapeImagePath,
      };
    } catch (error) {
      logger.error(`生成shape滤镜失败:`, error);
      throw new Error(
        `生成shape滤镜失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`
      );
    }
  }

  /**
   * 计算实际的图片尺寸（包含边框和图片放大）
   */
  private calculateActualImageDimensions(
    width: number,
    height: number,
    strokeWidth: number
  ): { width: number; height: number } {
    // 计算边框填充
    const strokePadding = strokeWidth > 0 ? Math.ceil(strokeWidth) : 0;
    const baseWidth = width + strokePadding * 2;
    const baseHeight = height + strokePadding * 2;

    // 获取ShapeImageGenerator的质量设置
    // 注意：这里我们需要访问ShapeImageGenerator的内部设置
    // 为了简化，我们假设使用默认设置，实际项目中可能需要更复杂的接口
    const imageScaleFactor = 1; // 默认不放大，实际应该从ShapeImageGenerator获取
    const enableImageScaling = false; // 默认不启用，实际应该从ShapeImageGenerator获取

    if (enableImageScaling) {
      return {
        width: baseWidth * imageScaleFactor,
        height: baseHeight * imageScaleFactor,
      };
    } else {
      return {
        width: baseWidth,
        height: baseHeight,
      };
    }
  }

  /**
   * 生成图片叠加滤镜
   */
  private generateImageOverlayFilter(
    x: number,
    y: number,
    targetWidth: number,
    targetHeight: number,
    actualImageWidth: number,
    actualImageHeight: number,
    rotation: number,
    startTime: number,
    endTime: number,
    index: number
  ): string {
    // 使用正确的输入索引，shape图片是第index+1个输入（因为第0个是背景）
    let filterString = `[${index + 1}:v]`;

    // 缩放到目标尺寸，但要考虑实际图片尺寸
    // 由于图片包含了边框空间，我们需要先缩放到实际图片尺寸，然后裁剪到目标尺寸
    filterString = this.applyScaleAndCrop(
      filterString,
      actualImageWidth,
      actualImageHeight
    );

    // 应用旋转
    if (rotation !== 0) {
      filterString = this.applyRotation(
        filterString,
        rotation,
        targetWidth,
        targetHeight
      );
    }

    // 设置格式
    filterString = this.applyFormat(filterString);

    // 设置时间范围
    filterString += `,setpts=PTS-STARTPTS+${startTime.toFixed(3)}/TB`;

    // 输出标签要与MediaOverlayFilterGenerator期望的一致
    return filterString + `[img${index}]`;
  }

  /**
   * 清理临时文件
   */
  cleanup(): void {
    this.shapeImageGenerator.cleanup();
    this.tempImagePaths = [];
    logger.info("已清理shape滤镜生成器的临时文件");
  }

  /**
   * 获取临时文件列表
   */
  getTempImagePaths(): string[] {
    return [...this.tempImagePaths];
  }
}
