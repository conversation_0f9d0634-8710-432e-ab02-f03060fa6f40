import React, { useEffect } from "react";
import { CssBaseline } from "@mui/material";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { StoreProvider as ElementProvideo } from "./store";
import { StoreProvider } from "./store/store-context";
import Editor from "./editor/Editor";
import Dashboard from "./components/Dashboard";
import { CustomThemeProvider } from "./theme/ThemeContext";
import { LanguageProvider } from "./i18n/LanguageContext";
import LoadingOverlay from "./components/LoadingOverlay";
import { preloadGifProcessor, cleanupGifCache } from "./utils/test/gifToSprite";
import { cleanupGifAnimations } from "./utils/test/fabricGif";

const App: React.FC = () => {
  useEffect(() => {
    // 应用启动时预热GIF处理器
    console.log("正在初始化GIF性能优化...");
    preloadGifProcessor();

    // 应用关闭时清理资源
    const handleBeforeUnload = () => {
      console.log("清理GIF相关资源...");
      cleanupGifCache();
      cleanupGifAnimations();
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      // 组件卸载时也清理资源
      cleanupGifCache();
      cleanupGifAnimations();
    };
  }, []);

  return (
    <LanguageProvider>
      <CustomThemeProvider>
        <CssBaseline />
        <Router>
          <StoreProvider>
            <ElementProvideo>
              <LoadingOverlay />
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/editor" element={<Editor />} />
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </ElementProvideo>
          </StoreProvider>
        </Router>
      </CustomThemeProvider>
    </LanguageProvider>
  );
};

export default App;
