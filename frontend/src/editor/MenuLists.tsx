import { Paper, IconButton, Tooltip } from "@mui/material";
import { observer } from "mobx-react-lite";
import { useLayoutStore } from "../store/store-context";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import VideocamIcon from "@mui/icons-material/Videocam";
import ImageIcon from "@mui/icons-material/Image";
import CategoryIcon from "@mui/icons-material/Category";
import AudiotrackIcon from "@mui/icons-material/Audiotrack";
import LayersIcon from "@mui/icons-material/Layers";
import React from "react";
import { StoreContext } from "../store";
import ClosedCaptionIcon from "@mui/icons-material/ClosedCaption";
import DashboardCustomizeIcon from "@mui/icons-material/DashboardCustomize";
import GifIcon from "@mui/icons-material/Gif";
const MenuList = observer(() => {
  const layoutStore = useLayoutStore();
  const store = React.useContext(StoreContext);

  // 定义菜单项配置，使代码更简洁和易维护
  const menuItems = [
    { id: "layers", icon: <LayersIcon />, option: "layers" },
    { id: "uploads", icon: <UploadFileIcon />, option: "uploads" },
    { id: "templates", icon: <DashboardCustomizeIcon />, option: "Templates" },
    { id: "texts", icon: <TextFieldsIcon />, option: "Text" },
    { id: "videos", icon: <VideocamIcon />, option: "Video" },
    {
      id: "images",
      icon: <ImageIcon sx={{ fontSize: 20 }} />,
      option: "Image",
    },
    {
      id: "gifs",
      icon: <GifIcon sx={{ fontSize: 20 }} />,
      option: "Gif",
    },
    { id: "audios", icon: <AudiotrackIcon />, option: "Audio" },
    { id: "shapes", icon: <CategoryIcon />, option: "Shape" },
    { id: "captions", icon: <ClosedCaptionIcon />, option: "Caption" },
  ];

  const handleMenuClick = (item: (typeof menuItems)[0]) => {
    if (item.option) {
      //@ts-ignore
      store.setSelectedMenuOption(item.option);
    }
    //@ts-ignore
    layoutStore.setActiveMenuItem(item.id);
    layoutStore.setShowMenuItem(true);
  };

  const getButtonStyle = (itemName: string) => {
    const isActive =
      layoutStore.showMenuItem && layoutStore.activeMenuItem === itemName;

    return {
      color: isActive ? "primary.main" : "text.secondary",
      bgcolor: isActive ? "action.selected" : "transparent",
      "&:hover": {
        bgcolor: "action.hover",
        color: isActive ? "primary.main" : "text.primary",
      },
      transition: "all 0.2s ease",
      my: 0.5,
    };
  };

  return (
    <Paper
      sx={{
        zIndex: 201,
        width: "60px",
        py: 1.5,
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        mt: 3,
        left: "16px",
        bgcolor: "grey.100",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      {menuItems.map((item) => (
        <Tooltip key={item.id} title={item.option} placement="right" arrow>
          <IconButton
            size="medium"
            onClick={() => handleMenuClick(item)}
            sx={getButtonStyle(item.id)}
          >
            {item.icon}
          </IconButton>
        </Tooltip>
      ))}
    </Paper>
  );
});

export default MenuList;
