"use client";
import {
  Box,
  Grid,
  Typography,
  Paper,
  useTheme,
  Tooltip,
  Tabs,
  Tab,
  Collapse,
} from "@mui/material";
import { observer } from "mobx-react";
import React, { useState, useEffect } from "react";
import { StoreContext } from "../../store";
import { ShapeType } from "../../types";

// 定义形状分类
const SHAPE_CATEGORIES = {
  basic: "基础图形",
  polygon: "多边形",
  special: "特殊图形",
};

// 定义形状资源
const SHAPE_RESOURCES: {
  name: string;
  type: ShapeType;
  category: keyof typeof SHAPE_CATEGORIES;
  description?: string;
  icon: React.ReactNode;
}[] = [
  {
    name: "矩形",
    type: "rect",
    category: "basic",
    description: "绘制一个矩形",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 40,
          bgcolor: "grey.400",
          borderRadius: 0,
        }}
      />
    ),
  },
  {
    name: "圆角矩形",
    type: "roundedRect",
    category: "basic",
    description: "绘制一个圆角矩形",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 40,
          bgcolor: "grey.400",
          borderRadius: 2,
        }}
      />
    ),
  },
  {
    name: "圆形",
    type: "circle",
    category: "basic",
    description: "绘制一个圆形",
    icon: (
      <Box
        sx={{
          width: 40,
          height: 40,
          bgcolor: "grey.400",
          borderRadius: "50%",
        }}
      />
    ),
  },
  {
    name: "椭圆",
    type: "ellipse",
    category: "basic",
    description: "绘制一个椭圆形",
    icon: (
      <Box
        sx={{
          width: 50,
          height: 30,
          bgcolor: "grey.400",
          borderRadius: "50%",
        }}
      />
    ),
  },
  {
    name: "三角形",
    type: "triangle",
    category: "polygon",
    description: "绘制一个三角形",
    icon: (
      <Box
        sx={{
          width: 0,
          height: 0,
          borderLeft: "20px solid transparent",
          borderRight: "20px solid transparent",
          borderBottom: "40px solid #9e9e9e",
        }}
      />
    ),
  },
  {
    name: "直线",
    type: "line",
    category: "basic",
    description: "绘制一条直线",
    icon: (
      <Box
        sx={{
          width: 50,
          height: 4,
          bgcolor: "grey.400",
        }}
      />
    ),
  },
  {
    name: "五边形",
    type: "pentagon",
    category: "polygon",
    description: "绘制一个五边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "grey.400",
            clipPath: "polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "六边形",
    type: "hexagon",
    category: "polygon",
    description: "绘制一个六边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "grey.400",
            clipPath:
              "polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "八边形",
    type: "octagon",
    category: "polygon",
    description: "绘制一个八边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "grey.400",
            clipPath:
              "polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%)",
          }}
        />
      </Box>
    ),
  },
  {
    name: "拱形",
    type: "arch",
    category: "special",
    description: "绘制一个拱形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 40,
            bgcolor: "grey.400",
            borderTopLeftRadius: "100%",
            borderTopRightRadius: "100%",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <Box
            sx={{
              position: "absolute",
              bottom: 0,
              left: 0,
              width: "100%",
              height: "50%",
              bgcolor: "grey.400",
            }}
          />
        </Box>
      </Box>
    ),
  },
  {
    name: "平行四边形",
    type: "parallelogram",
    category: "special",
    description: "绘制一个平行四边形",
    icon: (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          width: 40,
          height: 40,
        }}
      >
        <Box
          sx={{
            width: 40,
            height: 30,
            bgcolor: "grey.400",
            clipPath: "polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%)",
          }}
        />
      </Box>
    ),
  },
];

// 形状项组件
const ShapeItem = ({ shape, onClick, selected }) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Paper
      elevation={isHovered || selected ? 3 : 1}
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        p: 1,
        cursor: "pointer",
        transition: "all 0.2s",
        bgcolor: selected ? theme.palette.primary.light : "background.paper",
        "&:hover": {
          bgcolor: selected
            ? theme.palette.primary.light
            : theme.palette.action.hover,
          transform: "scale(1.05)",
        },
        border: selected ? `2px solid ${theme.palette.primary.main}` : "none",
      }}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          onClick();
          e.preventDefault();
        }
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: 60,
          width: "100%",
        }}
      >
        {shape.icon}
      </Box>
      <Typography
        variant="caption"
        align="center"
        sx={{
          mt: 1,
          color: selected ? theme.palette.primary.contrastText : "text.primary",
          fontWeight: selected ? "bold" : "normal",
        }}
      >
        {shape.name}
      </Typography>
    </Paper>
  );
};

export const Shapes = observer(() => {
  const store = React.useContext(StoreContext);
  const [activeTab, setActiveTab] =
    useState<keyof typeof SHAPE_CATEGORIES>("basic");
  const [selectedShape, setSelectedShape] = useState<ShapeType | null>(null);

  const handleTabChange = (
    event: React.SyntheticEvent,
    newValue: keyof typeof SHAPE_CATEGORIES
  ) => {
    setActiveTab(newValue);
  };

  const handleAddShape = (shapeType: ShapeType) => {
    store.addShapeElement(shapeType);
    setSelectedShape(shapeType);

    // 添加反馈效果
    setTimeout(() => {
      setSelectedShape(null);
    }, 500);
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 1,
        boxShadow: 1,
      }}
    >
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1">图形</Typography>
      </Box>

      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        aria-label="形状分类"
        variant="fullWidth"
        sx={{ borderBottom: 1, borderColor: "divider" }}
      >
        {Object.entries(SHAPE_CATEGORIES).map(([key, label]) => (
          <Tab
            key={key}
            label={label}
            value={key}
            sx={{
              minHeight: "40px",
              typography: "body2",
              textTransform: "none",
            }}
          />
        ))}
      </Tabs>

      <Box
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          p: 2,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
        }}
      >
        {Object.keys(SHAPE_CATEGORIES).map((category) => (
          <Collapse in={activeTab === category} key={category} unmountOnExit>
            <Grid container spacing={2}>
              {SHAPE_RESOURCES.filter(
                (shape) => shape.category === category
              ).map((shape, index) => (
                <Grid item xs={6} key={shape.type}>
                  <ShapeItem
                    shape={shape}
                    selected={selectedShape === shape.type}
                    onClick={() => handleAddShape(shape.type)}
                  />
                </Grid>
              ))}
            </Grid>
          </Collapse>
        ))}
      </Box>
    </Box>
  );
});
