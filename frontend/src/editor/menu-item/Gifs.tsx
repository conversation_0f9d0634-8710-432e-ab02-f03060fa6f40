import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useContext,
  useRef,
  memo,
} from "react";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  Alert,
  CircularProgress,
  ImageList,
  ImageListItem,
  IconButton,
  Tooltip,
  Button,
  Stack,
} from "@mui/material";
import {
  Search as SearchIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
} from "@mui/icons-material";
import { observer } from "mobx-react";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";
import { getUid } from "../../utils";
import {
  searchGifs,
  getGifCategories,
  GifFile,
} from "../../services/giphyService";

// 加载状态接口
interface LoadingState {
  [key: string]: boolean;
}

// GIF批次接口
interface GifBatch {
  id: number;
  gifs: GifFile[];
}

// GIF项目组件属性
interface GifItemProps {
  gif: GifFile;
  onGifAdd: (gif: GifFile, index: number) => void;
  index: number;
  isLoading: boolean;
  t: (key: string) => string;
}

// 常量定义
const SKELETON_COUNT = 6;
const PER_PAGE = 20;

// 优化的 GifItem 组件
const GifItem = memo(({ gif, onGifAdd, index, isLoading, t }: GifItemProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // 优化点击处理
  const handleClick = useCallback(() => {
    if (!isLoading) {
      onGifAdd(gif, index);
    }
  }, [isLoading, onGifAdd, gif, index]);

  // 优化加载完成处理
  const handleLoadedData = useCallback(() => {
    setIsLoaded(true);
  }, []);

  return (
    <ImageListItem
      onClick={handleClick}
      sx={{
        cursor: isLoading ? "not-allowed" : "pointer",
        position: "relative",
        borderRadius: 2,
        overflow: "hidden",
        transition: "all 0.3s ease",
        "&:hover": {
          transform: "translateY(-4px)",
          boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        },
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* GIF预览 */}
      <img
        src={gif.previewUrl}
        alt={gif.title}
        loading="lazy"
        onLoad={handleLoadedData}
        style={{
          width: "100%",
          height: "auto",
          display: "block",
          opacity: isLoaded && !isLoading ? 1 : 0.5,
          transition: "opacity 0.3s ease-in-out",
          borderRadius: 8,
        }}
      />

      {/* 加载指示器 */}
      {(!isLoaded || isLoading) && (
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            zIndex: 1,
          }}
        >
          <CircularProgress size={24} />
        </Box>
      )}

      {/* 悬停时显示的信息 */}
      {isHovered && isLoaded && !isLoading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            p: 1,
            borderRadius: 2,
          }}
        >
          {/* 顶部信息 */}
          <Box>
            {gif.username && (
              <Typography
                variant="caption"
                sx={{
                  color: "white",
                  backgroundColor: "rgba(0, 0, 0, 0.7)",
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: "0.7rem",
                }}
              >
                @{gif.username}
              </Typography>
            )}
          </Box>

          {/* 底部尺寸信息 */}
          <Box sx={{ textAlign: "center" }}>
            <Typography
              variant="caption"
              sx={{
                color: "white",
                backgroundColor: "rgba(0, 0, 0, 0.7)",
                px: 1,
                py: 0.5,
                borderRadius: 1,
                fontSize: "0.7rem",
              }}
            >
              {gif.width} × {gif.height}
            </Typography>
          </Box>
        </Box>
      )}
    </ImageListItem>
  );
});

// 骨架屏组件
const SkeletonList = () => {
  // 创建不同高度的skeleton数组，更像真实瀑布流
  const skeletonHeights = [180, 220, 260, 300, 340, 380];

  return (
    <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
      {Array.from({ length: SKELETON_COUNT }).map((_, index) => (
        <ImageListItem key={`skeleton-${index}`}>
          <Box
            sx={{
              width: "100%",
              height: skeletonHeights[index % skeletonHeights.length],
              backgroundColor: "grey.300",
              borderRadius: 2,
              animation: "pulse 1.5s ease-in-out infinite",
              "@keyframes pulse": {
                "0%": { opacity: 1 },
                "50%": { opacity: 0.5 },
                "100%": { opacity: 1 },
              },
            }}
          />
        </ImageListItem>
      ))}
    </ImageList>
  );
};

export const Gifs = observer(() => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();

  // 状态管理
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [loading, setLoading] = useState(false);
  const [loadingStates, setLoadingStates] = useState<LoadingState>({});
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // GIF数据状态
  const [initialGifs, setInitialGifs] = useState<GifFile[]>([]);
  const [gifBatches, setGifBatches] = useState<GifBatch[]>([]);
  const [batchCounter, setBatchCounter] = useState(0);
  const [batchLoading, setBatchLoading] = useState<number | null>(null);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [page, setPage] = useState(1);
  const [totalGifs, setTotalGifs] = useState(0);

  // 分类标签和滚动相关
  const categories = useMemo(() => getGifCategories(), []);
  const tagsContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // 滚动容器引用
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 防抖搜索
  const [debouncedQuery, setDebouncedQuery] = useState("");
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // 检查标签容器滚动状态
  const checkScroll = useCallback(() => {
    const container = tagsContainerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  }, []);

  // 标签容器滚动处理
  const handleScrollLeft = () => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: -200, behavior: "smooth" });
    }
  };

  const handleScrollRight = () => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: 200, behavior: "smooth" });
    }
  };

  // 监听标签容器滚动
  useEffect(() => {
    const container = tagsContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener("scroll", checkScroll);
      window.addEventListener("resize", checkScroll);
      return () => {
        container.removeEventListener("scroll", checkScroll);
        window.removeEventListener("resize", checkScroll);
      };
    }
  }, [checkScroll]);

  // 添加GIF到画布
  const onGifAdd = useCallback(
    async (gif: GifFile, index: number) => {
      if (!store) return;

      const gifId = getUid();
      setLoadingStates((prev) => ({ ...prev, [`gif-${index}`]: true }));

      try {
        // 创建图片元素来加载GIF
        const imgElement = document.createElement("img");
        imgElement.crossOrigin = "anonymous";
        imgElement.src = gif.url;
        imgElement.id = `gif-${gifId}`;

        // 添加到DOM（隐藏）
        imgElement.style.display = "none";
        document.body.appendChild(imgElement);

        // 等待图片加载完成
        await new Promise((resolve, reject) => {
          imgElement.onload = resolve;
          imgElement.onerror = reject;
        });

        // 添加到store
        const gifMetadata = {
          name: gif.title || `GIF ${store.editorElements.length + 1}`,
          width: gif.width,
          height: gif.height,
          isAnimated: true,
        };

        store.addGifElement(imgElement, gifId, gifMetadata);
      } catch (error) {
        console.error("添加GIF失败:", error);
        setErrorMessage(t("add_gif_error"));
      } finally {
        setLoadingStates((prev) => ({ ...prev, [`gif-${index}`]: false }));
      }
    },
    [store, t]
  );

  // 加载GIF数据
  const loadGifs = useCallback(
    async (pageNum: number, query?: string) => {
      if (loading) return;
      setLoading(true);
      setErrorMessage(null);

      // 如果不是第一页，设置当前批次为加载中
      if (pageNum > 1) {
        setBatchLoading((prev) => (prev || 0) + 1);
      }

      try {
        const searchTerm = query || "";
        const response = await searchGifs(searchTerm, pageNum, PER_PAGE);

        if (pageNum === 1) {
          // 第一页，重置所有数据
          setInitialGifs(response.gifs);
          setGifBatches([]);
          setBatchCounter(0);
          setTotalGifs(response.total || 0);
        } else {
          // 创建新的批次并添加到批次列表
          setBatchCounter((prev) => {
            const newBatchId = prev + 1;
            const newBatch: GifBatch = {
              id: newBatchId,
              gifs: response.gifs,
            };
            setGifBatches((prevBatches) => [...prevBatches, newBatch]);
            return newBatchId;
          });
        }

        setHasNextPage(response.hasMore);
        setPage(pageNum + 1);

        // 如果没有找到GIF，显示错误信息
        if (response.gifs.length === 0 && pageNum === 1) {
          setErrorMessage(t("no_gifs_found"));
        }
      } catch (error) {
        console.error("加载GIF失败:", error);
        setErrorMessage(t("load_gifs_error"));
      } finally {
        setLoading(false);
        setBatchLoading(null);
      }
    },
    [t] // 只保留必要的依赖项
  );

  // 初始加载标志
  const initialLoadRef = useRef(false);

  // 滚动加载更多
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

      if (scrollPercentage > 0.8 && hasNextPage && !loading && !batchLoading) {
        const query = debouncedQuery || selectedCategory;
        loadGifs(page, query);
      }
    },
    [
      hasNextPage,
      loading,
      batchLoading,
      page,
      debouncedQuery,
      selectedCategory,
      loadGifs,
    ]
  );

  // 初始加载
  useEffect(() => {
    if (!initialLoadRef.current) {
      initialLoadRef.current = true;
      loadGifs(1, "");
    }
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 搜索效果
  useEffect(() => {
    // 跳过初始加载
    if (!initialLoadRef.current) return;

    setHasNextPage(true);
    setPage(1);

    // 直接调用searchGifs，避免依赖loadGifs
    const searchTerm = debouncedQuery || selectedCategory;

    const performSearch = async () => {
      setLoading(true);
      setErrorMessage(null);

      try {
        const response = await searchGifs(searchTerm, 1, PER_PAGE);
        setInitialGifs(response.gifs);
        setGifBatches([]);
        setBatchCounter(0);
        setTotalGifs(response.total || 0);
        setHasNextPage(response.hasMore);
        setPage(2);

        if (response.gifs.length === 0) {
          setErrorMessage(t("no_gifs_found"));
        }
      } catch (error) {
        console.error("搜索GIF失败:", error);
        setErrorMessage(t("load_gifs_error"));
      } finally {
        setLoading(false);
      }
    };

    performSearch();
  }, [debouncedQuery, selectedCategory, t]);

  // 获取当前分类标签
  const getCurrentCategoryTags = () => {
    return Object.values(categories).flat().slice(0, 20); // 限制标签数量
  };

  // 判断是否有GIF
  const hasNoGifs =
    !loading && initialGifs.length === 0 && gifBatches.length === 0;

  // 计算当前显示的GIF总数
  const getCurrentDisplayedGifsCount = () => {
    return (
      initialGifs.length +
      gifBatches.reduce((sum, batch) => sum + batch.gifs.length, 0)
    );
  };

  // 渲染分隔标题的辅助函数
  const renderBatchTitle = (batchId: number) => (
    <Box
      sx={{
        height: "1px",
        bgcolor: "divider",
        my: 3,
        position: "relative",
      }}
    >
      <Box
        sx={{
          position: "absolute",
          top: -10,
          left: "50%",
          transform: "translateX(-50%)",
          bgcolor: "grey.100",
          px: 2,
        }}
      ></Box>
    </Box>
  );

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      {/* 标题栏 */}
      <Box
        sx={{
          bgcolor: "grey.100",
          height: 56,
          display: "flex",
          alignItems: "center",
          px: 3,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
          {t("gif_library")}
        </Typography>
      </Box>

      {/* 搜索框和标签区域 */}
      <Box sx={{ p: 2, bgcolor: "grey.100" }}>
        <TextField
          fullWidth
          size="small"
          placeholder={t("search_gifs")}
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setSelectedCategory("");
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              "&:hover": {
                "& > fieldset": { borderColor: "primary.main" },
              },
            },
          }}
        />

        {/* 分类标签 */}
        <Box
          sx={{
            width: 250,
            position: "relative",
            mt: 2,
            "&:hover .scroll-button": {
              display: "flex",
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              overflowX: "auto",
              scrollBehavior: "smooth",
              "&::-webkit-scrollbar": { display: "none" },
              msOverflowStyle: "none",
              scrollbarWidth: "none",
              px: 1,
              borderRadius: 2,
            }}
            ref={tagsContainerRef}
          >
            <Stack direction="row" spacing={1}>
              {getCurrentCategoryTags().map((tag) => (
                <Chip
                  key={tag}
                  label={tag}
                  onClick={() => {
                    if (selectedCategory === tag) {
                      setSelectedCategory("");
                    } else {
                      setSelectedCategory(tag);
                      setSearchQuery("");
                    }
                  }}
                  color={selectedCategory === tag ? "primary" : "default"}
                  sx={{ whiteSpace: "nowrap" }}
                />
              ))}
            </Stack>
          </Box>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              left: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.7,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollLeft}
          >
            <ChevronLeftIcon fontSize="small" />
          </IconButton>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              right: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.7,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollRight}
          >
            <ChevronRightIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* 错误提示 */}
      {errorMessage && !loading && (
        <Alert severity="warning" sx={{ m: 1, mb: 0 }}>
          {errorMessage}
          <Button
            size="small"
            onClick={() => {
              setErrorMessage(null);
              loadGifs(1, debouncedQuery || selectedCategory);
            }}
            sx={{ ml: 1 }}
          >
            {t("retry")}
          </Button>
        </Alert>
      )}

      {/* GIF列表区域 */}
      <Box
        ref={scrollContainerRef}
        onScroll={handleScroll}
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          overflow: "auto",
          px: 2,
          "&::-webkit-scrollbar": {
            width: "5px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            borderRadius: "5px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.3)",
            },
          },
        }}
      >
        {/* 无结果提示 */}
        {hasNoGifs && !loading && !errorMessage && (
          <Alert severity="info" sx={{ mb: 2, mt: 2 }}>
            {t("no_gifs_found")}
          </Alert>
        )}

        {/* 初始加载的骨架屏 */}
        {loading && page === 1 && <SkeletonList />}

        {/* 初始GIF列表 */}
        {initialGifs.length > 0 && (
          <ImageList
            variant="masonry"
            cols={2}
            gap={16}
            sx={{ margin: 0, mt: 2 }}
          >
            {initialGifs.map((gif, index) => (
              <GifItem
                key={gif.id}
                gif={gif}
                onGifAdd={onGifAdd}
                index={index}
                isLoading={loadingStates[`gif-${index}`] || false}
                t={t}
              />
            ))}
          </ImageList>
        )}

        {/* 批次GIF列表 */}
        {gifBatches.map((batch) => (
          <Box key={`batch-${batch.id}`}>
            {renderBatchTitle(batch.id)}
            <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
              {batch.gifs.map((gif, index) => {
                const gifIndex =
                  index + initialGifs.length + (batch.id - 1) * PER_PAGE;
                return (
                  <GifItem
                    key={`${gif.id}-batch-${batch.id}`}
                    gif={gif}
                    onGifAdd={onGifAdd}
                    index={gifIndex}
                    isLoading={loadingStates[`gif-${gifIndex}`] || false}
                    t={t}
                  />
                );
              })}
            </ImageList>
          </Box>
        ))}

        {/* 显示当前正在加载的批次骨架屏 */}
        {batchLoading !== null && (
          <>
            {renderBatchTitle(batchLoading)}
            <SkeletonList />
          </>
        )}

        {/* 结束指示器（当没有更多加载时） */}
        {!hasNextPage && !loading && initialGifs.length > 0 && (
          <Box
            sx={{
              p: 2,
              textAlign: "center",
              borderTop: "1px solid",
              borderColor: "divider",
              mt: 2,
            }}
          >
            <Typography variant="caption" color="text.secondary">
              {t("displayed_results")
                ? t("displayed_results")
                    .replace("{0}", getCurrentDisplayedGifsCount().toString())
                    .replace(
                      "{1}",
                      totalGifs ? totalGifs.toString() : t("unknown")
                    )
                : `已显示 ${getCurrentDisplayedGifsCount()} 个结果`}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
});

export default Gifs;
