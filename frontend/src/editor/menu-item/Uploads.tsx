// DEPRECATED: This component has been replaced by UnifiedFileUpload
// Please use UnifiedFileUpload from '@/components/common' instead
// This file is kept for backward compatibility

import { Box, Tab, Tabs, Typography } from "@mui/material";
import { useState } from "react";
import { UnifiedFileUpload } from "../../components/common";
import { useLanguage } from "../../i18n/LanguageContext";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`upload-tabpanel-${index}`}
      aria-labelledby={`upload-tab-${index}`}
      {...other}
      sx={{ mt: 2 }}
    >
      {value === index && children}
    </Box>
  );
}

export const Uploads = () => {
  const [value, setValue] = useState(0);
  const { t } = useLanguage();

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const handleFilesSelected = (files: File[]) => {
    console.log("Files selected:", files);
    // TODO: Implement file handling logic
  };

  return (
    <Box sx={{ flex: 1, borderRadius: 1 }}>
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1">{t("your_media")}</Typography>
      </Box>
      <Box sx={{ p: 2 }}>
        <Box sx={{ borderRadius: 1 }}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={value}
              onChange={handleChange}
              variant="fullWidth"
              aria-label="upload tabs"
              sx={{
                "& .MuiTab-root": {
                  textTransform: "none",
                  fontWeight: 500,
                },
              }}
            >
              <Tab label={t("project")} />
              <Tab label={t("workspace")} />
            </Tabs>
          </Box>
          <TabPanel value={value} index={0}>
            <UnifiedFileUpload
              mode="multiple"
              variant="button"
              onFilesSelected={handleFilesSelected}
              validation={{
                maxSize: 100 * 1024 * 1024, // 100MB
                allowedTypes: [
                  ".mp4",
                  ".mov",
                  ".mp3",
                  ".wav",
                  ".jpg",
                  ".png",
                  ".gif",
                ],
                maxCount: 10,
              }}
              placeholder={t("upload_media")}
              description={t("upload_media_description")}
            />
          </TabPanel>
          <TabPanel value={value} index={1}>
            <UnifiedFileUpload
              mode="multiple"
              variant="dropzone"
              onFilesSelected={handleFilesSelected}
              validation={{
                maxSize: 50 * 1024 * 1024, // 50MB
                maxCount: 5,
              }}
              placeholder={t("upload_to_workspace")}
              description={t("upload_workspace_description")}
            />
            <Typography variant="body2" sx={{ mt: 2 }}>
              {t("workspace_assets_available")}
            </Typography>
          </TabPanel>
        </Box>
      </Box>
    </Box>
  );
};
