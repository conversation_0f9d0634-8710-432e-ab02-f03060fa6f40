import { Box, IconButton, Paper } from "@mui/material";
import React from "react";
import CloseIcon from "@mui/icons-material/Close";
import { observer } from "mobx-react-lite";
import { useLayoutStore } from "../../store/store-context";
import { ElementsPanel } from "./ElementsPanel";
import { useLanguage } from "../../i18n/LanguageContext";
import { Texts } from "./Texts";
import { StoreContext } from "../../store";
import { Audios } from "./Audios";
import { Images } from "./Images";
import { Uploads } from "./Uploads";
import { Videos } from "./Videos";
import Captions from "./Captions";
import { Shapes } from "./Shapes";
import Templates from "./Templates";
import { Gifs } from "./Gifs";
// Add menu option type
type MenuOption =
  | "uploads"
  | "layers"
  | "Video"
  | "Audio"
  | "Image"
  | "Gif"
  | "Text"
  | "Caption"
  | "Shape"
  | "Templates";

// Create a mapping object for menu options to components
const MENU_COMPONENTS: Record<MenuOption, React.ComponentType> = {
  uploads: Uploads,
  layers: ElementsPanel,
  Video: Videos,
  Audio: Audios,
  Image: Images,
  Gif: Gifs,
  Text: Texts,
  Caption: Captions,
  Shape: Shapes,
  Templates: Templates,
};

const Container = observer(({ children }: { children: React.ReactNode }) => {
  const layoutStore = useLayoutStore();

  return (
    <Box
      sx={{
        width: "360px",
        height: "calc(100% - 90px)",
        mt: 3,
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        left: layoutStore.showMenuItem ? 10 : "-360px",
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        visibility: "visible",
        zIndex: 200,
        borderRadius: 2,
        display: "flex",
      }}
    >
      <Box sx={{ width: "74px" }} />
      <Paper
        sx={{
          bgcolor: "grey.100",
          flex: 1,
          position: "relative",

          display: "flex",
          height: "100%",
        }}
      >
        <IconButton
          onClick={() => layoutStore.setShowMenuItem(false)}
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
            width: 32,
            height: 32,
            color: "text.secondary",
          }}
        >
          <CloseIcon sx={{ fontSize: 16 }} />
        </IconButton>
        {children}
      </Paper>
    </Box>
  );
});

export const MenuItem = observer(() => {
  const store = React.useContext(StoreContext);
  const selectedMenuOption = store.selectedMenuOption as MenuOption;
  const { t } = useLanguage();

  const SelectedComponent = MENU_COMPONENTS[selectedMenuOption];

  return <Container>{SelectedComponent && <SelectedComponent />}</Container>;
});
