import TuneIcon from "@mui/icons-material/Tune";
import {
  Box,
  Grid,
  IconButton,
  Popover,
  Tab,
  Tabs,
  Typography,
} from "@mui/material";
import { observer } from "mobx-react";
import { useContext, useState, useCallback, useMemo, useEffect } from "react";
import { AnimationResource } from "../entity/AnimationResource";
import { StoreContext } from "../../store";
import { getUid } from "../../utils";
import { useLanguage } from "../../i18n/LanguageContext";

type TransitionType =
  | "none"
  | "fadeIn"
  | "fadeOut"
  | "slideIn"
  | "slideOut"
  | "slideUp"
  | "slideDown"
  | "slideLeft"
  | "slideRight"
  | "wipeUp"
  | "wipeDown"
  | "wipeLeft"
  | "rectAngle"
  | "elastic"
  | "breathe";

interface Transition {
  type: TransitionType;
  id: string;
  direction: string;
  useClipPath: boolean;
}

// Constants for animations
const ANIMATION_TYPES = {
  FADE_IN: "fadeIn",
  FADE_OUT: "fadeOut",
  SLIDE_UP: "slideUp",
  // ... other types
} as const;

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`animation-tabpanel-${index}`}
      aria-labelledby={`animation-tab-${index}`}
      {...other}
      sx={{ mt: 2 }}
    >
      {value === index && children}
    </Box>
  );
}

const TransitionOption = ({
  src,
  label,
  onClick,
  isActive,
  transitionId = "none",
  direction = "none",
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const store = useContext(StoreContext);
  const { t } = useLanguage();

  const handleSettingsClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
    },
    []
  );

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const handleClick = useCallback(() => {
    const id = onClick();
    if (id === "remove") {
      store.removeAnimation(transitionId);
    }
  }, [onClick, store, transitionId]);

  const open = Boolean(anchorEl);

  // 转换标签为翻译键
  const getTranslationKey = (labelText: string): string => {
    switch (labelText) {
      case "None":
        return "none";
      case "Fade":
        return "fade";
      case "Slide Down":
        return "slide_down";
      case "Slide Up":
        return "slide_up";
      case "Slide Left":
        return "slide_left";
      case "Slide Right":
        return "slide_right";
      case "Wipe Down":
        return "wipe_down";
      case "Wipe Up":
        return "wipe_up";
      case "Wipe Left":
        return "wipe_left";
      case "Wipe Right":
        return "wipe_right";
      case "Breathe":
        return "breathe";
      default:
        return labelText.toLowerCase().replace(/\s+/g, "_");
    }
  };

  return (
    <Box
      sx={{
        textAlign: "center",
        m: 0.5,
        cursor: "pointer",
        transition: "transform 0.2s",
        position: "relative",
        "&:hover": {
          transform: "scale(1.05)",
        },
      }}
    >
      <Box
        sx={{
          backgroundColor: label === "Fade" ? "#f5f5f5" : "#ffffff",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          transition: "box-shadow 0.2s",
          border: isActive ? "2px solid red" : "none",
          position: "relative",
          borderRadius: "8px",
          overflow: "hidden",
          "&:hover": {
            boxShadow: "0 4px 8px rgba(0,0,0,0.15)",
          },
        }}
      >
        {isActive && (
          <IconButton
            size="small"
            onClick={handleSettingsClick}
            sx={{
              position: "absolute",
              right: 1,
              top: 1,
              zIndex: 100,
              padding: "2px",
              backgroundColor: "rgba(255, 255, 255, 0.8)",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.9)",
              },
            }}
          >
            <TuneIcon sx={{ fontSize: 14 }} />
          </IconButton>
        )}
        <img
          onClick={handleClick}
          src={src}
          alt={t(getTranslationKey(label))}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "contain",
          }}
        />
      </Box>
      <Typography
        variant="caption"
        sx={{
          fontWeight: 450,
          color: "#666",
          fontSize: "0.7rem",
        }}
      >
        {t(getTranslationKey(label))}
      </Typography>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        {store.getAnimation(transitionId) && (
          <AnimationResource
            key={transitionId}
            animation={store.getAnimation(transitionId)}
          />
        )}
      </Popover>
    </Box>
  );
};

interface TransitionConfig {
  src: string;
  label: string;
  type: TransitionType;
  direction?: string;
  useClipPath?: boolean;
}

const TRANSITIONS: Record<string, TransitionConfig[]> = {
  in: [
    {
      src: "/assets/animations/transition-none.jpg",
      label: "None",
      type: "none",
    },
    { src: "/assets/animations/fade.gif", label: "Fade", type: "fadeIn" },
    {
      src: "/assets/animations/slide-up.gif",
      label: "Slide Down",
      type: "slideIn",
      direction: "top",
    },
    {
      src: "/assets/animations/slide-down.gif",
      label: "Slide Up",
      type: "slideIn",
      direction: "bottom",
    },
    {
      src: "/assets/animations/slide-left.gif",
      label: "Slide Left",
      type: "slideIn",
      direction: "left",
    },
    {
      src: "/assets/animations/slide-right.gif",
      label: "Slide Right",
      type: "slideIn",
      direction: "right",
    },
    {
      src: "/assets/animations/wipe-up.gif",
      label: "Wipe Down",
      type: "slideIn",
      direction: "top",
      useClipPath: true,
    },
    {
      src: "/assets/animations/wipe-down.gif",
      label: "Wipe Up",
      type: "slideIn",
      direction: "bottom",
      useClipPath: true,
    },
    {
      src: "/assets/animations/wipe-left.gif",
      label: "Wipe Left",
      type: "slideIn",
      direction: "left",
      useClipPath: true,
    },
    {
      src: "/assets/animations/wipe-right.gif",
      label: "Wipe Right",
      type: "slideIn",
      direction: "right",
      useClipPath: true,
    },
    {
      src: "/assets/animations/rectangle.gif",
      label: "Breathe",
      type: "breathe",
      direction: "right",
    },
  ],
  out: [
    {
      src: "/assets/animations/transition-none.jpg",
      label: "None",
      type: "none",
    },
    { src: "/assets/animations/fade.gif", label: "Fade", type: "fadeOut" },
    {
      src: "/assets/animations/slide-up.gif",
      label: "Slide Up",
      type: "slideOut",
      direction: "top",
    },
    {
      src: "/assets/animations/slide-down.gif",
      label: "Slide Down",
      type: "slideOut",
      direction: "bottom",
    },
    {
      src: "/assets/animations/slide-left.gif",
      label: "Slide Left",
      type: "slideOut",
      direction: "left",
    },
    {
      src: "/assets/animations/slide-right.gif",
      label: "Slide Right",
      type: "slideOut",
      direction: "right",
    },
    {
      src: "/assets/animations/wipe-up.gif",
      label: "Wipe Down",
      type: "slideOut",
      direction: "top",
      useClipPath: true,
    },
    {
      src: "/assets/animations/wipe-down.gif",
      label: "Wipe Up",
      type: "slideOut",
      direction: "bottom",
      useClipPath: true,
    },
    {
      src: "/assets/animations/wipe-left.gif",
      label: "Wipe Left",
      type: "slideOut",
      direction: "left",
      useClipPath: true,
    },
    {
      src: "/assets/animations/wipe-right.gif",
      label: "Wipe Right",
      type: "slideOut",
      direction: "right",
      useClipPath: true,
    },
  ],
};

const TransitionGrid = ({
  group,
  transitions,
  selectedTransitions,
  onTransitionClick,
  findAnimationIdByType,
  store,
}) => (
  <Grid container spacing={1}>
    {transitions.map((config) => (
      <Grid item xs={4} key={`${config.label}-${config.direction || ""}`}>
        <TransitionOption
          src={config.src}
          label={config.label}
          direction={config.direction}
          transitionId={findAnimationIdByType(config.type)}
          isActive={
            config.type === "none"
              ? false
              : selectedTransitions.some(
                  (transition) =>
                    transition.type === config.type &&
                    transition.direction === (config.direction || "none") &&
                    transition.useClipPath === (config.useClipPath || false)
                )
          }
          onClick={() => {
            return onTransitionClick(
              config.type,
              group,
              config.direction || "none",
              config.useClipPath || false
            );
          }}
        />
      </Grid>
    ))}
  </Grid>
);

const Animations = observer(() => {
  const [value, setValue] = useState(0);
  const store = useContext(StoreContext);
  const selectedElement = store.selectedElement;
  const { t } = useLanguage();

  // 使用useMemo缓存选中元素的动画，避免重复计算
  const selectedElementAnimations = useMemo(
    () =>
      store.animations.filter(
        (animation) => animation.targetId === selectedElement?.id
      ),
    [store.animations, selectedElement?.id]
  );

  // 按组分类的动画映射，提高查找效率
  const animationsByGroup = useMemo(() => {
    const groupMap = new Map<string, any[]>();
    selectedElementAnimations.forEach((animation) => {
      const group = animation.group || "";
      if (!groupMap.has(group)) {
        groupMap.set(group, []);
      }
      groupMap.get(group)?.push(animation);
    });
    return groupMap;
  }, [selectedElementAnimations]);

  const [selectedTransitions, setSelectedTransitions] = useState<Transition[]>(
    []
  );

  // 使用useCallback缓存findAnimationIdByType函数
  const findAnimationIdByType = useCallback(
    (type: TransitionType): string => {
      const animation = selectedElementAnimations.find(
        (item) => item.type === type
      );
      return animation?.id || "none";
    },
    [selectedElementAnimations]
  );

  const addAnimation = useCallback(
    (
      group: string,
      type: TransitionType,
      direction: string,
      useClipPath: boolean,
      properties = {}
    ) => {
      const animationId = getUid();
      store.addAnimation({
        id: animationId,
        type,
        targetId: selectedElement?.id ?? "",
        duration: 2000,
        group,
        properties: { direction: direction, useClipPath },
      });
      return animationId;
    },
    [selectedElement?.id, store]
  );

  const handleTransitionClick = useCallback(
    (
      label: TransitionType,
      group: string,
      direction: string,
      useClipPath: boolean
    ) => {
      if (label === "none") {
        // 获取该组的动画，并从store中删除
        store.removeAnimationByGroup(selectedElement?.id || "", group);

        // 直接按组过滤状态，而不是通过多次查询
        setSelectedTransitions((prev) =>
          prev.filter((transition) => {
            const animation = selectedElementAnimations.find(
              (anim) => anim.id === transition.id
            );
            return !animation || animation.group !== group;
          })
        );
        return "remove";
      }

      // 检查是否点击了已选中的动画
      const existingTransition = selectedTransitions.find(
        (transition) =>
          transition.type === label &&
          selectedElementAnimations.find(
            (anim) => anim.id === transition.id && anim.group === group
          )
      );

      if (existingTransition) {
        // 如果点击已选中动画，则取消选中
        store.removeAnimation(existingTransition.id);
        setSelectedTransitions((prev) =>
          prev.filter((item) => item.id !== existingTransition.id)
        );
        return "remove";
      }

      // 获取同组动画
      const sameGroupAnimations = animationsByGroup.get(group) || [];

      // 删除同组中已存在的动画
      sameGroupAnimations.forEach((animation) => {
        store.removeAnimation(animation.id);
      });

      // 从状态中移除同组动画
      setSelectedTransitions((prev) =>
        prev.filter((transition) => {
          const animation = selectedElementAnimations.find(
            (anim) => anim.id === transition.id
          );
          return !animation || animation.group !== group;
        })
      );

      // 添加新动画
      const animationId = addAnimation(group, label, direction, useClipPath);
      if (animationId) {
        setSelectedTransitions((prev) => [
          ...prev,
          { type: label, id: animationId, direction, useClipPath },
        ]);
      }

      store.canvas.renderAll();
      return animationId;
    },
    [
      selectedTransitions,
      addAnimation,
      selectedElementAnimations,
      selectedElement,
      store,
      animationsByGroup,
    ]
  );

  const handleChange = useCallback(
    (event: React.SyntheticEvent, newValue: number) => {
      setValue(newValue);
    },
    []
  );

  // 当selectedElement或store.animations变化时同步更新selectedTransitions
  useEffect(() => {
    setSelectedTransitions(
      selectedElementAnimations.map((animation) => ({
        type: animation.type as TransitionType,
        id: animation.id,
        direction: (animation.properties as any)?.direction || "none",
        useClipPath: (animation.properties as any)?.useClipPath || false,
      }))
    );
  }, [selectedElementAnimations]);

  // 使用useMemo优化TransitionGrid渲染
  const inTransitionGrid = useMemo(
    () => (
      <TransitionGrid
        group="in"
        transitions={TRANSITIONS.in}
        selectedTransitions={selectedTransitions}
        onTransitionClick={handleTransitionClick}
        findAnimationIdByType={findAnimationIdByType}
        store={store}
      />
    ),
    [selectedTransitions, handleTransitionClick, findAnimationIdByType, store]
  );

  const outTransitionGrid = useMemo(
    () => (
      <TransitionGrid
        group="out"
        transitions={TRANSITIONS.out}
        selectedTransitions={selectedTransitions}
        onTransitionClick={handleTransitionClick}
        findAnimationIdByType={findAnimationIdByType}
        store={store}
      />
    ),
    [selectedTransitions, handleTransitionClick, findAnimationIdByType, store]
  );

  return (
    <Box
      sx={{ width: "100%", flex: 1, display: "flex", flexDirection: "column" }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          {t("animations")}
        </Typography>
      </Box>
      <Tabs
        value={value}
        onChange={handleChange}
        variant="fullWidth"
        aria-label="animation tabs"
      >
        <Tab label={t("animation_in")} />
        <Tab label={t("animation_out")} />
      </Tabs>

      <Box
        sx={{
          m: 2,
          width: "250px",
          height: "100%",
          overflow: "auto",

          "&::-webkit-scrollbar": {
            width: "1px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(255, 255, 255, 0.2)",
            borderRadius: "1px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.3)",
            },
          },
        }}
      >
        <TabPanel value={value} index={0}>
          <Box>{inTransitionGrid}</Box>
        </TabPanel>
        <TabPanel value={value} index={1}>
          <Box>{outTransitionGrid}</Box>
        </TabPanel>
      </Box>
    </Box>
  );
});

export default Animations;
