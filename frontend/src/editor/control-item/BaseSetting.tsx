import React, { useCallback, useContext, useState, useEffect } from "react";
import { observer } from "mobx-react";
import debounce from "lodash.debounce";

// Material-UI Icons
import AlignHorizontalCenterIcon from "@mui/icons-material/AlignHorizontalCenter";
import AlignHorizontalLeftIcon from "@mui/icons-material/AlignHorizontalLeft";
import AlignHorizontalRightIcon from "@mui/icons-material/AlignHorizontalRight";
import AlignVerticalBottomIcon from "@mui/icons-material/AlignVerticalBottom";
import AlignVerticalCenterIcon from "@mui/icons-material/AlignVerticalCenter";
import AlignVerticalTopIcon from "@mui/icons-material/AlignVerticalTop";
import DeleteIcon from "@mui/icons-material/Delete";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import FlipIcon from "@mui/icons-material/Flip";
import FormatAlignJustifyIcon from "@mui/icons-material/FormatAlignJustify";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import LockIcon from "@mui/icons-material/Lock";
import LockOpenIcon from "@mui/icons-material/LockOpen";
import Opacity from "@mui/icons-material/Opacity";

// Material-UI Components
import {
  Box,
  Grid,
  IconButton,
  Popover,
  Slider,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";

// Local imports
import { StoreContext } from "../../store";
import { EditorElement } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";

// Constants
const OPACITY_SLIDER_CONFIG = {
  step: 1,
  min: 0,
  max: 100,
  color: "#1976d2",
  thumbSize: 12,
};

const POPOVER_WIDTH = 250;

const DEBOUNCE_WAIT = 100;

const ALIGNMENT_BUTTONS = [
  {
    icon: AlignHorizontalLeftIcon,
    align: "left",
    tooltipKey: "align_left",
  },
  {
    icon: AlignHorizontalCenterIcon,
    align: "center",
    tooltipKey: "align_center",
  },
  {
    icon: AlignHorizontalRightIcon,
    align: "right",
    tooltipKey: "align_right",
  },
  {
    icon: FormatAlignJustifyIcon,
    align: "justify",
    tooltipKey: "justify",
  },
  {
    icon: AlignVerticalTopIcon,
    align: "top",
    tooltipKey: "align_top",
  },
  {
    icon: AlignVerticalCenterIcon,
    align: "middle",
    tooltipKey: "align_middle",
  },
  {
    icon: AlignVerticalBottomIcon,
    align: "bottom",
    tooltipKey: "align_bottom",
  },
] as const;

const SLIDER_STYLES = {
  color: OPACITY_SLIDER_CONFIG.color,
  "& .MuiSlider-thumb": {
    width: OPACITY_SLIDER_CONFIG.thumbSize,
    height: OPACITY_SLIDER_CONFIG.thumbSize,
    transition: "0.3s cubic-bezier(.47,1.64,.41,.8)",
    "&:before": {
      boxShadow: "0 2px 12px 0 rgba(0,0,0,0.4)",
    },
    "&:hover, &.Mui-focusVisible": {
      boxShadow: `0px 0px 0px 8px rgb(25 118 210 / 16%)`,
    },
  },
  "& .MuiSlider-rail": {
    opacity: 0.5,
  },
};

type FlipType = "horizontal" | "vertical";
type LayoutProperty = "x" | "y" | "width" | "height" | "rotation";

const BaseSetting = observer(() => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  // 获取全局选中的元素 - 参考CaptionText的模式
  const element = store.selectedElement;

  // 细粒度状态管理 - 参考CaptionText的模式，为每个属性单独使用useState
  const [opacity, setOpacity] = useState(element?.opacity ?? 1);
  const [x, setX] = useState(element?.placement?.x ?? 0);
  const [y, setY] = useState(element?.placement?.y ?? 0);
  const [width, setWidth] = useState(element?.placement?.width ?? 0);
  const [height, setHeight] = useState(element?.placement?.height ?? 0);
  const [rotation, setRotation] = useState(element?.placement?.rotation ?? 0);
  const [scaleX, setScaleX] = useState(element?.placement?.scaleX ?? 1);
  const [scaleY, setScaleY] = useState(element?.placement?.scaleY ?? 1);

  // 同步全局选中元素状态变化 - 只在元素切换时同步，避免循环更新
  useEffect(() => {
    if (element) {
      setOpacity(element.opacity ?? 1);
      setX(element.placement?.x ?? 0);
      setY(element.placement?.y ?? 0);
      setWidth(element.placement?.width ?? 0);
      setHeight(element.placement?.height ?? 0);
      setRotation(element.placement?.rotation ?? 0);
      setScaleX(element.placement?.scaleX ?? 1);
      setScaleY(element.placement?.scaleY ?? 1);
    }
  }, [element?.id]); // 只监听元素ID变化，避免属性变化导致的循环更新

  const isOpacityPopoverOpen = Boolean(anchorEl);

  // Event handlers with useCallback for performance
  const handleAlign = useCallback(
    (alignType: string) => {
      if (!element) return;
      store.alignElement(element.id, alignType);
    },
    [element, store]
  );

  const handleLock = useCallback(() => {
    if (!element) return;
    store.toggleLockElement(element.id);
  }, [element, store]);

  const handleClone = useCallback(() => {
    if (!element) return;
    store.cloneElement(element.id);
  }, [element, store]);

  const handleDelete = useCallback(() => {
    if (!element) return;
    store.deleteElement(element.id);
  }, [element, store]);

  const handleFullscreen = useCallback(() => {
    if (!element) return;
    store.setElementFullscreen(element.id);
  }, [element, store]);

  const handleOpacityClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleOpacityClose = () => {
    setAnchorEl(null);
  };

  // 创建稳定的debounce函数，避免因状态变化导致重新创建
  const updateStoreLayout = useCallback(
    debounce((placementUpdates, currentElement, currentStates) => {
      if (!currentElement?.fabricObject || !placementUpdates) return;

      // 构建完整的placement对象
      const currentPlacement = currentElement.placement || {};
      const newPlacement = {
        ...currentPlacement,
        ...currentStates,
        ...placementUpdates,
      };

      currentElement.fabricObject.set({
        left: newPlacement.x,
        top: newPlacement.y,
        width: newPlacement.width,
        height: newPlacement.height,
        angle: newPlacement.rotation,
      });

      // if (currentElement.type === "text") {
      //   currentElement.fabricObject.set({
      //     scaleX: 1,
      //     scaleY: 1,
      //   });
      // }

      const updatedElement = {
        ...currentElement,
        placement: newPlacement,
      };

      store.updateEditorElement(updatedElement);
      store.canvas.requestRenderAll();
    }, DEBOUNCE_WAIT),
    [store] // 只依赖store，避免状态变化导致debounce重新创建
  );

  const handleFlip = useCallback(
    (flipType: FlipType) => {
      if (!element?.fabricObject) return;

      let newScaleX = scaleX || 1;
      let newScaleY = scaleY || 1;

      if (flipType === "horizontal") {
        newScaleX = -newScaleX;
      } else {
        newScaleY = -newScaleY;
      }

      element.fabricObject.set({
        scaleX: newScaleX,
        scaleY: newScaleY,
      });

      // 立即更新本地状态
      setScaleX(newScaleX);
      setScaleY(newScaleY);

      // 更新store状态，传递当前元素和状态
      const currentStates = {
        x,
        y,
        width,
        height,
        rotation,
        scaleX: newScaleX,
        scaleY: newScaleY,
      };
      updateStoreLayout(
        {
          scaleX: newScaleX,
          scaleY: newScaleY,
        },
        element,
        currentStates
      );
    },
    [element, scaleX, scaleY, x, y, width, height, rotation, updateStoreLayout]
  );

  const updateStoreOpacity = useCallback(
    debounce((newOpacity: number) => {
      if (element) {
        store.updateElementOpacity(element.id, newOpacity);
      }
    }, DEBOUNCE_WAIT),
    [element, store]
  );

  // 透明度变化处理器 - 参考CaptionText的即时更新模式
  const handleOpacityChange = (_: Event, newValue: number | number[]) => {
    const newOpacity = (newValue as number) / 100;
    // 立即更新本地状态，提供即时响应
    setOpacity(newOpacity);
    // 使用debounce更新store状态，避免频繁更新
    updateStoreOpacity(newOpacity);
  };

  // 布局变化处理器 - 使用细粒度状态更新
  const handleLayoutChange = (property: LayoutProperty, value: number) => {
    if (!element) return;

    let actualValue = value;

    // 对于文本元素的宽度和高度，需要反向计算实际值
    if (
      element?.type === "text" &&
      (property === "width" || property === "height")
    ) {
      const currentScaleX = scaleX || 1;
      const currentScaleY = scaleY || 1;
      const scale = property === "width" ? currentScaleX : currentScaleY;
      actualValue = value / scale; // 反向计算原始值
    }

    // 立即更新对应的本地状态，提供即时响应
    let newStates = { x, y, width, height, rotation, scaleX, scaleY };
    if (property === "x") {
      setX(actualValue);
      newStates.x = actualValue;
    } else if (property === "y") {
      setY(actualValue);
      newStates.y = actualValue;
    } else if (property === "width") {
      setWidth(actualValue);
      newStates.width = actualValue;
    } else if (property === "height") {
      setHeight(actualValue);
      newStates.height = actualValue;
    } else if (property === "rotation") {
      setRotation(actualValue);
      newStates.rotation = actualValue;
    }

    // 使用debounce更新store状态，传递当前元素和状态
    updateStoreLayout({ [property]: actualValue }, element, newStates);
  };

  // Early return for no element selected
  if (!element) {
    return (
      <Box sx={{ height: "100%", p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t("no_element_selected")}
        </Typography>
      </Box>
    );
  }

  // Helper function to create layout input - 使用细粒度状态
  const createLayoutInput = (
    label: string,
    property: LayoutProperty,
    value: number | undefined
  ) => {
    let displayValue = value || 0;

    // 对于文本元素的宽度和高度，考虑缩放系数
    if (
      element?.type === "text" &&
      (property === "width" || property === "height")
    ) {
      const currentScaleX = scaleX || 1;
      const currentScaleY = scaleY || 1;
      const scale = property === "width" ? currentScaleX : currentScaleY;
      displayValue = (value || 0) * scale;
    }

    return (
      <TextField
        label={label}
        type="number"
        value={Math.round(displayValue)}
        onChange={(e) => handleLayoutChange(property, Number(e.target.value))}
        fullWidth
        size="small"
      />
    );
  };

  return (
    <Box sx={{ height: "100%" }}>
      {/* Action Buttons */}
      <Stack direction="row" justifyContent="space-between" sx={{ mb: 2 }}>
        <Tooltip title={element.locked ? t("unlock") : t("lock")}>
          <IconButton onClick={handleLock}>
            {element.locked ? (
              <LockIcon fontSize="small" />
            ) : (
              <LockOpenIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>

        <Tooltip title={t("opacity")}>
          <IconButton onClick={handleOpacityClick}>
            <Opacity fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("clone")}>
          <IconButton onClick={handleClone}>
            <FileCopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("delete")}>
          <IconButton onClick={handleDelete}>
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("fullscreen")}>
          <IconButton onClick={handleFullscreen}>
            <FullscreenIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("flip_horizontal")}>
          <IconButton onClick={() => handleFlip("horizontal")}>
            <FlipIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("flip_vertical")}>
          <IconButton onClick={() => handleFlip("vertical")}>
            <FlipIcon fontSize="small" sx={{ transform: "rotate(90deg)" }} />
          </IconButton>
        </Tooltip>
      </Stack>

      {/* Opacity Popover */}
      <Popover
        open={isOpacityPopoverOpen}
        anchorEl={anchorEl}
        onClose={handleOpacityClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Box sx={{ p: 2, width: POPOVER_WIDTH }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Slider
              value={opacity * 100}
              onChange={handleOpacityChange}
              aria-labelledby="opacity-slider"
              valueLabelDisplay="off"
              step={OPACITY_SLIDER_CONFIG.step}
              min={OPACITY_SLIDER_CONFIG.min}
              max={OPACITY_SLIDER_CONFIG.max}
              sx={SLIDER_STYLES}
            />
            <Typography
              variant="body2"
              sx={{ minWidth: 35, textAlign: "right", color: "text.secondary" }}
            >
              {Math.round(opacity * 100)}%
            </Typography>
          </Stack>
        </Box>
      </Popover>

      {/* Alignment Section */}
      <Typography variant="subtitle2" sx={{ mb: 1, color: "text.secondary" }}>
        {t("alignment")}
      </Typography>

      <Stack direction="row">
        {ALIGNMENT_BUTTONS.map(({ icon: Icon, align, tooltipKey }, index) => (
          <Tooltip key={index} title={t(tooltipKey)}>
            <IconButton onClick={() => handleAlign(align)}>
              <Icon fontSize="small" />
            </IconButton>
          </Tooltip>
        ))}
      </Stack>

      {/* Position Section */}
      <Typography variant="subtitle2" sx={{ my: 1, color: "text.secondary" }}>
        {t("position")}
      </Typography>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={6}>
          {createLayoutInput(t("position_x"), "x", x)}
        </Grid>
        <Grid item xs={6}>
          {createLayoutInput(t("position_y"), "y", y)}
        </Grid>
        {/* <Grid item xs={6}>
          {createLayoutInput(t("width"), "width", width)}
        </Grid>
        <Grid item xs={6}>
          {createLayoutInput(t("height"), "height", height)}
        </Grid> */}
        <Grid item xs={12}>
          {createLayoutInput(t("rotation"), "rotation", rotation)}
        </Grid>
      </Grid>
    </Box>
  );
});

export default BaseSetting;
