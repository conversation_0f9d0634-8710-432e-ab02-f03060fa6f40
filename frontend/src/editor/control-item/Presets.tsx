import { Box, Typography, Grid } from "@mui/material";
import BlockIcon from "@mui/icons-material/Block";
import { useState } from "react";
import React from "react";
import { StoreContext } from "../../store";
import { EditorElement, TextEditorElement, CaptionStyle } from "../../types";

interface PresetsProps {
  element: EditorElement | null;
  // 新增属性来标识是否为字幕模式
  isCaptionMode?: boolean;
}

const Presets = ({ element, isCaptionMode = false }: PresetsProps) => {
  const [selectedPreset, setSelectedPreset] = useState<number | null>(null);
  const store = React.useContext(StoreContext);

  // 字幕模式下不需要检查element
  if (!isCaptionMode && (!element || !element.properties)) {
    return <></>;
  }

  // 文本元素模式下的类型转换
  const textElement = !isCaptionMode ? (element as TextEditorElement) : null;

  const presets = [
    { id: 1, icon: <BlockIcon sx={{ color: "#666666" }} /> },
    {
      id: 2,
      text: "Text",
      backgroundColor: "#000000",
      fontColor: "#ffffff",
      strokeWidth: 1,
      strokeColor: "#ffffff",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 2,
      shadowOffsetY: 2,
      style: {
        WebkitTextStroke: "1px #ffffff",
        textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
      },
    },
    {
      id: 3,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#000000",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 4,
      text: "Text",
      backgroundColor: "#ffffff",
      fontColor: "#000000",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 2,
      shadowOffsetY: 2,
      style: {
        textShadow: "2px 2px 2px rgba(0,0,0)",
        WebkitTextStroke: "0px #000000",
      },
    },
    {
      id: 5,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#000000",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 2,
      shadowOffsetY: 2,
      style: {
        textShadow: "2px 2px 4px rgba(0,0,0)",
        WebkitTextStroke: "0px #000000",
      },
    },
    {
      id: 6,
      text: "Text",
      backgroundColor: "#6200ee",
      fontColor: "#ffffff",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 7,
      text: "Text",
      backgroundColor: "#ffd700",
      fontColor: "#000000",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 8,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#0066ff",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "rgba(0,102,255,1)",
      shadowOffsetX: 2,
      shadowOffsetY: 2,
      style: {
        textShadow: "2px 2px 4px rgba(0,102,255)",
        WebkitTextStroke: "0px #000000",
      },
    },
    {
      id: 9,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#000000",
      strokeWidth: 1,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "1px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 10,
      text: "Text",
      backgroundColor: "#000000",
      fontColor: "#00ff9d",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 11,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#ff69b4",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "rgba(255,105,180,0.3)",
      shadowOffsetX: 2,
      shadowOffsetY: 2,
      style: {
        textShadow: "2px 2px 4px rgba(255,105,180,0.3)",
        WebkitTextStroke: "0px rgba(0,0,0)",
      },
    },
    {
      id: 12,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#008000",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },

    {
      id: 14,
      text: "Text",
      backgroundColor: "#000000",
      fontColor: "#ffd700",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
    },
    {
      id: 15,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#87ceeb",
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 16,
      text: "Text",
      backgroundColor: "transparent",
      fontColor: "#ffffff",
      strokeWidth: 1,
      strokeColor: "#ff0000",
      shadowBlur: 0,
      shadowColor: "#000000",
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      style: {
        WebkitTextStroke: "1px #ff0000",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
  ];

  const handlePresetClick = (presetId: number) => {
    setSelectedPreset(presetId);

    // Get the selected preset
    const preset = presets.find((preset) => preset.id === presetId);
    if (!preset) return;

    if (isCaptionMode) {
      // 字幕模式：更新全局字幕样式
      if (preset.id === 1) {
        // 重置为默认样式
        store.captionManager.resetGlobalCaptionStyle();
        return;
      }

      // 构建字幕样式更新对象
      const captionStyleUpdate: Partial<CaptionStyle> = {
        backgroundColor: preset.backgroundColor,
        fontColor: preset.fontColor,
        strokeWidth: preset.strokeWidth || 0,
        strokeColor: preset.strokeColor || "#000000",
        shadowBlur: preset.shadowBlur || 0,
        shadowColor: preset.shadowColor || "#000000",
        shadowOffsetX: preset.shadowOffsetX || 0,
        shadowOffsetY: preset.shadowOffsetY || 0,
      };

      // 更新全局字幕样式
      store.updateGlobalCaptionStyle(captionStyleUpdate);
    } else {
      // 文本元素模式：更新选中的文本元素
      if (
        !store.selectedElement ||
        !store.selectedElement.fabricObject ||
        !textElement
      ) {
        return;
      }

      const styleUpdate: any = {
        backgroundColor: preset.backgroundColor,
        fontColor: preset.fontColor,
      };

      // Add shadow settings if the preset has textShadow
      if (preset.style?.textShadow) {
        // Parse the text shadow values
        const shadowMatch = preset.style.textShadow.match(
          /([0-9]+px) ([0-9]+px) ([0-9]+px) (rgba?\([^)]+\))/
        );
        if (shadowMatch) {
          styleUpdate.shadowOffsetX = parseInt(shadowMatch[1]);
          styleUpdate.shadowOffsetY = parseInt(shadowMatch[2]);
          styleUpdate.shadowBlur = parseInt(shadowMatch[3]);
          styleUpdate.shadowColor = shadowMatch[4];
        }
      }

      if (preset.style?.WebkitTextStroke) {
        // Parse the border values (e.g., "2px solid #000000")
        const borderMatch = preset.style.WebkitTextStroke.match(
          /([0-9]+px) (#[0-9a-f]+)/i
        );
        if (borderMatch) {
          styleUpdate.strokeWidth = parseInt(borderMatch[1]);
          styleUpdate.strokeColor = borderMatch[2];
        }
      }
      console.log("WebkitTextStroke", styleUpdate);
      store.updateTextStyle(textElement.id, styleUpdate);
    }
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderBottom: "1px solid rgba(0, 0, 0, 0.08)",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
          Presets
        </Typography>
      </Box>
      <Box
        sx={{
          height: "100%",
          overflow: "auto",
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            borderRadius: "3px",
          },
        }}
      >
        <Grid container spacing={2} sx={{ p: 2 }}>
          {presets.map((preset) => (
            <Grid item xs={4} key={preset.id}>
              <Box
                onClick={() => handlePresetClick(preset.id)}
                sx={{
                  aspectRatio: "1",
                  backgroundColor: "white",
                  borderRadius: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  p: 1.5,
                  border:
                    selectedPreset === preset.id
                      ? "2px solid #1976d2"
                      : "1px solid rgba(0, 0, 0, 0.12)",
                  cursor: "pointer",
                  transition: "all 0.2s ease-in-out",
                  "&:hover": {
                    transform: "scale(1.02)",
                    boxShadow: "0 2px 12px rgba(0,0,0,0.08)",
                  },
                  position: "relative",
                }}
              >
                {preset.id === 1 ? (
                  <BlockIcon sx={{ color: "#666", fontSize: 24 }} />
                ) : (
                  <Typography
                    sx={{
                      fontSize: "1rem",
                      fontWeight: 600,
                      padding: "3px 8px",
                      borderRadius: "4px",
                      backgroundColor: preset.backgroundColor,
                      color: preset.fontColor,
                      ...preset.style,
                    }}
                  >
                    {preset.text}
                  </Typography>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default Presets;
