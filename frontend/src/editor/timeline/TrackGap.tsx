"use client";
import { Box } from "@mui/material";
import React from "react";
import { useDroppable } from "@dnd-kit/core";
import { TrackType } from "../../types";

// 轨道间隔组件的属性
interface TrackGapProps {
  id: string;
  beforeTrackId: string | null;
  afterTrackId: string | null;
  trackType: TrackType;
  isVisible?: boolean; // 新增可见性控制属性
}

// 轨道间隔组件
export const TrackGap: React.FC<TrackGapProps> = ({
  id,
  beforeTrackId,
  afterTrackId,
  trackType,
  isVisible = true, // 默认为可见
}) => {
  // 配置可放置区域
  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      type: "gap",
      beforeTrackId,
      afterTrackId,
      trackType,
    },
  });

  return (
    <Box
      ref={setNodeRef}
      id={id}
      sx={{
        width: "100%",
        height: "2px",
        my: 0.125,
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)", // 更平滑的过渡效果
        borderRadius: 1,
        position: "relative",
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? "auto" : "none",
        "&::before": {
          content: '""',
          position: "absolute",
          top: "50%",
          left: "5%", // 扩大宽度范围
          right: "5%",
          height: "2px",
          transform: "translateY(-50%)",
          backgroundColor: "transparent",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        },
        "&::after": {
          content: '""',
          position: "absolute",
          top: "50%",
          left: 0,
          right: 0,
          height: "0px",
          transform: "translateY(-50%)",
          pointerEvents: "none",
          opacity: 0,
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        },
        "&:hover:not(.track-gap-highlight)::before": {
          // 悬停效果
          backgroundColor: "rgba(180, 180, 180, 0.2)",
          animation: "pulse 2s infinite ease-in-out",
        },
        "&.track-gap-highlight": {
          height: "2px",
          "&::before": {
            backgroundColor: "primary.main",
            height: "2px",
          },
          // "&::after": {
          //   opacity: 1,
          //   height: "2px", // 稍微增加高度
          //   border: "2px dashed rgba(33, 150, 243, 0.7)",
          //   borderRadius: "2px",
          // },
        },
      }}
    />
  );
};
