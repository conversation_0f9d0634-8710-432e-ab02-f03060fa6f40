"use client";
import { Box } from "@mui/material";
import { observer } from "mobx-react";
import React, { useContext, useLayoutEffect, useState, useMemo } from "react";
import { StoreContext } from "../../store";
import { getTimelineContainerWidth } from "../../utils/timeUtils";
import { TrackView } from "./TrackView";
import { CaptionsTrackView } from "./CaptionsTrackView";
import { TrackDndContext, useTrackDnd } from "./TrackDndContext";
import { TrackGap } from "./TrackGap";
import { TrackType } from "../../types";

// 轨道列表组件 - 使用TrackDnd上下文
const TrackList = observer(() => {
  const store = useContext(StoreContext);
  const { activeElement } = useTrackDnd();
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  // 获取所有轨道及其元素
  const trackElements = store.trackManager.getAllTrackElements();

  // 获取当前拖拽元素的类型
  const activeElementType = activeElement?.type as TrackType | undefined;

  // 判断是否有活动元素
  const hasActiveElement = !!activeElement;

  // 判断是否为空状态
  const isEmpty = useMemo(() => {
    return (
      trackElements.length === 0 &&
      (!store.captions || store.captions.length === 0)
    );
  }, [trackElements.length, store.captions]);

  // 更新容器宽度
  useLayoutEffect(() => {
    const updateWidth = () => {
      setContainerWidth(getTimelineContainerWidth());
    };

    updateWidth();
    window.addEventListener("resize", updateWidth);
    return () => window.removeEventListener("resize", updateWidth);
  }, []);

  // 如果没有轨道和字幕，显示空状态
  if (isEmpty) {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "text.secondary",
          fontSize: "0.875rem",
          fontStyle: "italic",
        }}
      >
        No timeline elements found. Add elements to get started.
      </Box>
    );
  }

  // 渲染轨道和轨道间隔
  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        overflowY: "auto",
        overflowX: "hidden",
        paddingTop: "30px",
      }}
    >
      {/* 第一个轨道之前的间隔 */}
      {trackElements.length > 0 && (
        <TrackGap
          id={`gap-before-${trackElements[0].track.id}`}
          beforeTrackId={null}
          afterTrackId={trackElements[0].track.id}
          trackType={activeElementType || "text"}
          isVisible={hasActiveElement}
        />
      )}

      {/* 渲染轨道和轨道之间的间隔 */}
      {trackElements.map(({ track, elements }, index) => {
        const isLastTrack = index === trackElements.length - 1;
        const nextTrack = !isLastTrack ? trackElements[index + 1].track : null;

        return (
          <React.Fragment key={track.id}>
            <TrackView track={track} elements={elements} isDraggable={true} />

            {/* 在每个轨道后添加间隔（除了最后一个轨道） */}
            {!isLastTrack && nextTrack && (
              <TrackGap
                id={`gap-between-${track.id}-${nextTrack.id}`}
                beforeTrackId={track.id}
                afterTrackId={nextTrack.id}
                trackType={activeElementType || "text"}
                isVisible={hasActiveElement}
              />
            )}
          </React.Fragment>
        );
      })}

      {/* 最后一个轨道之后的间隔 */}
      {trackElements.length > 0 && (
        <TrackGap
          id={`gap-after-${trackElements[trackElements.length - 1].track.id}`}
          beforeTrackId={trackElements[trackElements.length - 1].track.id}
          afterTrackId={null}
          trackType={activeElementType || "text"}
          isVisible={hasActiveElement}
        />
      )}

      {/* 渲染字幕轨道 */}
      {store.captions && store.captions.length > 0 && (
        <CaptionsTrackView captions={store.captions} />
      )}
    </Box>
  );
});

// 时间线列表组件 - 按轨道分组显示
export const TimeLineListByTrack = observer(() => {
  return (
    <TrackDndContext>
      <TrackList />
    </TrackDndContext>
  );
});
