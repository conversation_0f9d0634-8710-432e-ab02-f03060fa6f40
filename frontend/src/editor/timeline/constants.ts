// 时间线高度常量
export const TIMELINE_HEIGHT = "36px";
export const TRACK_HEIGHT = "36px";

// 元素类型到图标/颜色的映射
export const ELEMENT_COLORS = {
  text: "#ff9800", // Indigo
  image: "#4caf50", // Green
  video: "#f44336", // Red
  audio: "#3f51b5", // Blue
  default: "#9e9e9e", // Default grey
};

// 轨道类型对应的颜色
export const TRACK_COLORS = {
  media: "#4caf50", // 使用原来的image颜色作为media颜色
  audio: "#3f51b5",
  text: "#ff9800",
  caption: "#9c27b0",
} as const;

// 字幕颜色
export const CAPTION_COLOR = "#9c27b0"; // 紫色

// 元素内容渲染映射
export const ELEMENT_CONTENT_MAP = {
  text: (element) => ({
    icon: "TextFields",
    text: element.properties?.text ?? element.name,
  }),
  image: (element) => ({
    icon: "Image",
    text: element.name,
  }),
  video: (element) => ({
    icon: "VideoFile",
    text: element.name,
  }),
  audio: (element) => ({
    icon: "AudioFile",
    text: element.name,
  }),
};

// 拖拽相关常量
export const DRAG_CONSTANTS = {
  VERTICAL_DRAG_THRESHOLD: 3,
  HORIZONTAL_DRAG_THRESHOLD: 1,
  DRAG_ACTIVATION_DISTANCE: 3,
  DRAG_TOLERANCE: 3,
  DRAG_DELAY: 0,
};

// 吸附相关常量
export const SNAP_CONSTANTS = {
  SNAP_THRESHOLD: 100,
  SNAP_COLOR: "#ff9800",
};

// 最小元素持续时间（毫秒）
export const MIN_ELEMENT_DURATION = 1000; // 1秒
