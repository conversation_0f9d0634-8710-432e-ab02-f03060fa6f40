import { Box } from "@mui/material";
import React from "react";

interface SnapIndicatorProps {
  position: "left" | "right";
  isVisible: boolean;
}

/**
 * 吸附指示器组件
 * 在元素吸附到其他元素时显示视觉反馈
 */
export const SnapIndicator: React.FC<SnapIndicatorProps> = ({
  position,
  isVisible,
}) => {
  if (!isVisible) return null;

  return (
    <Box
      sx={{
        position: "absolute",
        top: 0,
        bottom: 0,
        [position]: 0,
        width: "4px",
        backgroundColor: "#ff9800",
        borderRadius: "2px",
        opacity: 0.8,
        boxShadow: "0 0 8px rgba(255,152,0,0.5)",
        zIndex: 30,
      }}
    />
  );
};
