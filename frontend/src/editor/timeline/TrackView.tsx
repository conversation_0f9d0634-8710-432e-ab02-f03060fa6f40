"use client";
import { Box, useTheme } from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useContext,
  useRef,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { StoreContext } from "../../store";
import { EditorElement, Track } from "../../types";
import { TimeFrameView } from "./TimeLine";
import { useDroppable } from "@dnd-kit/core";
import { useTrackDnd } from "./TrackDndContext";
import TrackContextMenu from "./menu/TrackContextMenu";

// 轨道类型对应的颜色
const TRACK_COLORS = {
  media: "#4caf50", // 使用原来的image颜色作为media颜色
  audio: "#3f51b5",
  text: "#ff9800",
  caption: "#9c27b0",
} as const;

// 轨道高度常量
const TRACK_HEIGHT = "38px";

interface TrackViewProps {
  track: Track;
  elements: EditorElement[];
  isDraggable?: boolean;
}

export const TrackView = observer(
  ({ track, elements, isDraggable }: TrackViewProps) => {
    const store = useContext(StoreContext);
    const theme = useTheme();
    const trackRef = useRef<HTMLDivElement>(null);
    const [isHighlighted, setIsHighlighted] = useState(false);
    const [isTypeMismatch, setIsTypeMismatch] = useState(false);
    const [contextMenu, setContextMenu] = useState<{
      mouseX: number;
      mouseY: number;
    } | null>(null);

    const { overTrack, activeElement } = useTrackDnd();

    const { setNodeRef } = useDroppable({
      id: `track-${track.id}`,
      data: {
        type: "track",
        trackId: track.id,
        trackType: track.type,
      },
    });

    const handleTimeFrameChange = useCallback(
      (element: EditorElement, start: number, end: number) => {
        // 设置isDragEnd为true，确保更新maxDuration
        store.updateEditorElementTimeFrame(element, { start, end }, true);
        store.trackManager.fixTrackElementsOverlap(track.id);
      },
      [store, track.id]
    );

    // 处理右键菜单
    const handleContextMenu = useCallback((event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setContextMenu({
        mouseX: event.clientX,
        mouseY: event.clientY,
      });
    }, []);

    const handleCloseContextMenu = useCallback(() => {
      setContextMenu(null);
    }, []);

    // 检测当前轨道是否被高亮
    useEffect(() => {
      setIsHighlighted(overTrack?.id === track.id);
    }, [overTrack, track.id]);

    // 检测元素类型与轨道类型是否匹配
    useEffect(() => {
      if (overTrack?.id === track.id && activeElement) {
        // 当轨道被悬停且有活动元素时，检查类型是否匹配
        let isMatch = false;

        // 对于image和video类型的元素，它们可以放在media类型的轨道中
        if (
          (activeElement.type === "image" || activeElement.type === "video") &&
          track.type === "media"
        ) {
          isMatch = true;
        } else {
          isMatch = activeElement.type === track.type;
        }

        setIsTypeMismatch(!isMatch);
      } else {
        setIsTypeMismatch(false);
      }
    }, [overTrack, track.id, track.type, activeElement]);

    const trackStyles = useMemo(
      () => ({
        position: "relative",
        width: "100%",
        bgcolor: "grey.100",
        height: TRACK_HEIGHT,
        my: 0.25, // 减小上下边距，从1减小到0.25（从8px减小到2px）
        borderRadius: 1,
        transition: "all 0.2s ease",
        border: isTypeMismatch
          ? "2px dashed rgba(244, 67, 54, 0.8)" // 类型不匹配时显示红色虚线边框
          : isHighlighted
          ? "2px dashed rgba(33, 150, 243, 0.6)"
          : "none",
        ...(elements?.length > 0 && {
          marginLeft: "10px",
          overflow: "visible",
        }),
      }),
      [theme.palette.mode, isTypeMismatch, isHighlighted, elements?.length]
    );

    const setRefs = useCallback(
      (node: HTMLDivElement | null) => {
        trackRef.current = node;
        if (node) setNodeRef(node);
      },
      [setNodeRef]
    );

    const trackClassName = useMemo(
      () =>
        `timeline-track ${isHighlighted ? "track-highlight" : ""} ${
          isTypeMismatch ? "track-type-mismatch" : ""
        }`,
      [isHighlighted, isTypeMismatch]
    );

    if (!elements?.length) {
      return (
        <>
          <Box
            ref={setRefs}
            sx={trackStyles}
            onContextMenu={handleContextMenu}
          />
          <TrackContextMenu
            track={track}
            contextMenu={contextMenu}
            handleClose={handleCloseContextMenu}
          />
        </>
      );
    }

    return (
      <>
        <Box
          ref={setRefs}
          className={trackClassName}
          sx={trackStyles}
          onContextMenu={handleContextMenu}
        >
          <Box
            sx={{
              position: "absolute",
              inset: 0,
              width: "100%",
              height: "100%",
            }}
          >
            {elements.map((element) => (
              <TimeFrameView
                key={element.id}
                element={element}
                handleTimeFrameChange={handleTimeFrameChange}
                allElements={elements}
                isDraggable={isDraggable}
              />
            ))}
          </Box>
        </Box>
        <TrackContextMenu
          track={track}
          contextMenu={contextMenu}
          handleClose={handleCloseContextMenu}
        />
      </>
    );
  }
);
