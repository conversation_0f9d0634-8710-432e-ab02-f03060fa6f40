import React from "react";
import {
  <PERSON>u,
  <PERSON>uItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import { ContentPaste, AddCircleOutline } from "@mui/icons-material";
import { StoreContext } from "../../../store";
import { Track } from "../../../types";
import {
  contextMenuStyles,
  contextMenuItemStyles,
  menuDividerStyles,
} from "../styles";

interface TrackContextMenuProps {
  track: Track;
  contextMenu: {
    mouseX: number;
    mouseY: number;
  } | null;
  handleClose: () => void;
}

const TrackContextMenu: React.FC<TrackContextMenuProps> = ({
  track,
  contextMenu,
  handleClose,
}) => {
  const store = React.useContext(StoreContext);

  // Check if there are any copied/cut elements
  const hasClipboardElement = React.useMemo(() => {
    // Here we only check if there is a currently selected element, can be extended to check the actual clipboard
    return store.selectedElement !== null;
  }, [store.selectedElement]);

  const handlePaste = () => {
    if (!store.selectedElement) return;

    // Copy the selected element
    const element = store.selectedElement;

    // Create a new element (clone the selected element)
    store.cloneElement(element.id);

    // Get the latest created element (the cloned element should be the last one in the editorElements array)
    const newElement = store.editorElements[store.editorElements.length - 1];

    if (newElement) {
      // Move the new element to the current track
      store.trackManager.moveElementToTrack(newElement.id, track.id);

      // Set the start time of the new element to the current indicator position
      const indicatorTime = store.currentTimeInMs;
      const duration = newElement.timeFrame.end - newElement.timeFrame.start;

      store.updateEditorElementTimeFrame(
        newElement,
        {
          start: indicatorTime,
          end: indicatorTime + duration,
        },
        true
      );

      // Select the new element
      store.setSelectedElement(newElement);
    }

    handleClose();
  };

  return (
    <Menu
      open={contextMenu !== null}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu !== null
          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
          : undefined
      }
      PaperProps={{ sx: contextMenuStyles.paper }}
    >
      <MenuItem
        onClick={handlePaste}
        sx={contextMenuItemStyles}
        disabled={!hasClipboardElement}
      >
        <ListItemIcon>
          <ContentPaste fontSize="small" />
        </ListItemIcon>
        <ListItemText>Paste</ListItemText>
      </MenuItem>
      {/* <MenuItem
        onClick={() => {
          handleClose();
        }}
        sx={contextMenuItemStyles}
      >
        <ListItemIcon>
          <AddCircleOutline fontSize="small" />
        </ListItemIcon>
        <ListItemText>Add New Element</ListItemText>
      </MenuItem> */}
    </Menu>
  );
};

export default TrackContextMenu;
