import axios from "axios";

// Giphy API 配置
const GIPHY_API_KEY =
  process.env.REACT_APP_GIPHY_API_KEY || "s41NuFIILTaVkumOsH9ilUkpt8GiktLf";
const GIPHY_BASE_URL = "https://api.giphy.com/v1/gifs";
const PER_PAGE = 20; // 每页GIF数量

// GIF接口定义
export interface GiphyGif {
  id: string;
  title: string;
  url: string;
  images: {
    original: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    fixed_height: {
      url: string;
      width: string;
      height: string;
    };
    fixed_width: {
      url: string;
      width: string;
      height: string;
    };
    preview_gif: {
      url: string;
      width: string;
      height: string;
    };
    downsized: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    downsized_medium: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
  };
  user?: {
    username: string;
    display_name: string;
    avatar_url: string;
  };
  import_datetime: string;
  trending_datetime: string;
  rating: string;
}

// 统一的GIF格式
export interface GifFile {
  id: string;
  title: string;
  width: number;
  height: number;
  url: string;
  previewUrl: string;
  thumbnailUrl: string;
  size?: number;
  username?: string;
  rating: string;
}

// API响应接口
export interface GiphyApiResponse {
  data: GiphyGif[];
  pagination: {
    total_count: number;
    count: number;
    offset: number;
  };
  meta: {
    status: number;
    msg: string;
    response_id: string;
  };
}

// 统一的API响应格式
export interface GifApiResponse {
  gifs: GifFile[];
  total: number;
  hasMore: boolean;
}

// 将Giphy API响应转换为统一格式
const mapGiphyGifsToCommonFormat = (giphyGifs: GiphyGif[]): GifFile[] => {
  return giphyGifs.map((gif) => ({
    id: gif.id,
    title: gif.title || "Untitled GIF",
    width: parseInt(gif.images.original.width),
    height: parseInt(gif.images.original.height),
    url: gif.images.original.url,
    previewUrl: gif.images.fixed_height.url,
    thumbnailUrl: gif.images.preview_gif.url,
    size: gif.images.original.size
      ? parseInt(gif.images.original.size)
      : undefined,
    username: gif.user?.username,
    rating: gif.rating,
  }));
};

// 获取热门GIF
export const getTrendingGifs = async (
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  try {
    const offset = (page - 1) * perPage;
    const response = await axios.get<GiphyApiResponse>(
      `${GIPHY_BASE_URL}/trending`,
      {
        params: {
          api_key: GIPHY_API_KEY,
          limit: perPage,
          offset: offset,
          rating: "g", // 只获取适合所有年龄的内容
        },
      }
    );

    const gifs = mapGiphyGifsToCommonFormat(response.data.data);
    const total = response.data.pagination.total_count;
    const hasMore = offset + perPage < total;

    return {
      gifs,
      total,
      hasMore,
    };
  } catch (error) {
    console.error("获取热门GIF失败:", error);
    return { gifs: [], total: 0, hasMore: false };
  }
};

// 搜索GIF
export const searchGifs = async (
  query: string,
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  try {
    if (!query.trim()) {
      return getTrendingGifs(page, perPage);
    }

    const offset = (page - 1) * perPage;
    const response = await axios.get<GiphyApiResponse>(
      `${GIPHY_BASE_URL}/search`,
      {
        params: {
          api_key: GIPHY_API_KEY,
          q: query,
          limit: perPage,
          offset: offset,
          rating: "g",
          lang: "en",
        },
      }
    );

    const gifs = mapGiphyGifsToCommonFormat(response.data.data);
    const total = response.data.pagination.total_count;
    const hasMore = offset + perPage < total;

    return {
      gifs,
      total,
      hasMore,
    };
  } catch (error) {
    console.error("搜索GIF失败:", error);
    return { gifs: [], total: 0, hasMore: false };
  }
};

// 根据分类获取GIF
export const getGifsByCategory = async (
  category: string,
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  return searchGifs(category, page, perPage);
};

// 获取GIF分类标签
export const getGifCategories = () => {
  return {
    popular: [
      "funny",
      "reaction",
      "happy",
      "love",
      "excited",
      "dance",
      "celebrate",
      "thumbs up",
    ],
    emotions: [
      "laugh",
      "cry",
      "angry",
      "surprised",
      "confused",
      "tired",
      "nervous",
      "proud",
    ],
    actions: ["wave", "clap", "jump", "run"],
    animals: ["cat", "dog", "bird"],
    nature: ["rain", "snow"],
    technology: ["computer", "phone"],
  };
};

// 获取随机GIF
export const getRandomGifs = async (
  tag?: string,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  try {
    const response = await axios.get<{ data: GiphyGif }>(
      `${GIPHY_BASE_URL}/random`,
      {
        params: {
          api_key: GIPHY_API_KEY,
          tag: tag || "",
          rating: "g",
        },
      }
    );

    // 随机API只返回一个GIF，我们需要多次调用来获取多个
    const promises = Array(perPage)
      .fill(null)
      .map(() =>
        axios.get<{ data: GiphyGif }>(`${GIPHY_BASE_URL}/random`, {
          params: {
            api_key: GIPHY_API_KEY,
            tag: tag || "",
            rating: "g",
          },
        })
      );

    const responses = await Promise.all(promises);
    const giphyGifs = responses.map((res) => res.data.data);
    const gifs = mapGiphyGifsToCommonFormat(giphyGifs);

    return {
      gifs,
      total: gifs.length,
      hasMore: false, // 随机GIF没有分页概念
    };
  } catch (error) {
    console.error("获取随机GIF失败:", error);
    return { gifs: [], total: 0, hasMore: false };
  }
};

// 验证API密钥
export const validateApiKey = async (): Promise<boolean> => {
  try {
    const response = await axios.get(`${GIPHY_BASE_URL}/trending`, {
      params: {
        api_key: GIPHY_API_KEY,
        limit: 1,
      },
    });
    return response.status === 200;
  } catch (error) {
    console.error("Giphy API密钥验证失败:", error);
    return false;
  }
};
