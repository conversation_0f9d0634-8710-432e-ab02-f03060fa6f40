import { parseGIF, decompressFrames } from "gifuct-js";

// 缓存已处理的GIF，避免重复处理
const gifCache = new Map<string, any>();

// 获取GIF的缓存键
const getCacheKey = (
  gif: string | File,
  maxWidth?: number,
  maxHeight?: number,
  maxDuration?: number
): string => {
  if (typeof gif === "string") {
    return `${gif}_${maxWidth || 0}_${maxHeight || 0}_${maxDuration || 0}`;
  }
  // 对于File对象，使用文件大小和修改时间作为键
  return `${gif.size}_${gif.lastModified}_${maxWidth || 0}_${maxHeight || 0}_${
    maxDuration || 0
  }`;
};

// 优化的Canvas池，避免重复创建和销毁Canvas
class CanvasPool {
  private static pools = new Map<string, HTMLCanvasElement[]>();

  static getCanvas(
    type: string,
    width?: number,
    height?: number
  ): HTMLCanvasElement {
    const key = `${type}_${width || 0}_${height || 0}`;
    let pool = this.pools.get(key);

    if (!pool) {
      pool = [];
      this.pools.set(key, pool);
    }

    if (pool.length > 0) {
      const canvas = pool.pop()!;
      // 重置canvas状态
      const ctx = canvas.getContext("2d")!;
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.setTransform(1, 0, 0, 1, 0, 0);
      return canvas;
    }

    const canvas = document.createElement("canvas");
    if (width) canvas.width = width;
    if (height) canvas.height = height;
    return canvas;
  }

  static returnCanvas(canvas: HTMLCanvasElement, type: string) {
    const key = `${type}_${canvas.width}_${canvas.height}`;
    let pool = this.pools.get(key);

    if (!pool) {
      pool = [];
      this.pools.set(key, pool);
    }

    // 限制池大小，避免内存泄漏
    if (pool.length < 5) {
      pool.push(canvas);
    } else {
      canvas.remove();
    }
  }

  // 清理池，释放内存
  static cleanup() {
    this.pools.forEach((pool) => {
      pool.forEach((canvas) => canvas.remove());
    });
    this.pools.clear();
  }
}

/**
 * 优化的GIF转精灵图函数
 * @param {string|File} gif 可以是URL、dataURL或File对象
 * @param {number} maxWidth 可选，缩放到最大宽度
 * @param {number} maxHeight 可选，缩放到最大高度
 * @param {number} maxDuration 可选，以毫秒为单位减少GIF帧到最大持续时间
 * @returns {Promise<any>} 错误对象或转换后的GIF精灵图数据
 */
export const gifToSprite = async (
  gif: string | File,
  maxWidth?: number,
  maxHeight?: number,
  maxDuration?: number
): Promise<any> => {
  // 检查缓存
  const cacheKey = getCacheKey(gif, maxWidth, maxHeight, maxDuration);
  if (gifCache.has(cacheKey)) {
    return gifCache.get(cacheKey);
  }

  let arrayBuffer: ArrayBuffer;
  let error: any;
  let frames: any[];

  try {
    // 获取ArrayBuffer
    if (gif instanceof File) {
      arrayBuffer = await gif.arrayBuffer();
    } else {
      const response = await fetch(gif);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      arrayBuffer = await response.arrayBuffer();
    }

    // 解析和解压GIF帧
    frames = decompressFrames(parseGIF(arrayBuffer), true);

    if (!frames || !frames.length) {
      throw new Error("No frames found in GIF");
    }
  } catch (err) {
    error = err;
    console.error("GIF processing error:", error);
    return { error };
  }

  try {
    // 使用Canvas池获取Canvas
    const dataCanvas = CanvasPool.getCanvas("data");
    const dataCtx = dataCanvas.getContext("2d")!;
    const frameCanvas = CanvasPool.getCanvas("frame");
    const frameCtx = frameCanvas.getContext("2d")!;
    const spriteCanvas = CanvasPool.getCanvas("sprite");
    const spriteCtx = spriteCanvas.getContext("2d")!;

    // 获取帧尺寸和延迟
    const firstFrame = frames[0];
    let width = firstFrame.dims.width;
    let height = firstFrame.dims.height;

    // 优化延迟计算 - 使用第一个有效延迟或默认值
    let delay = firstFrame.delay || 100;
    if (delay < 20) delay = 100; // 防止过快的动画

    // 处理最大持续时间
    const duration = frames.length * delay;
    const actualMaxDuration = maxDuration || duration;
    if (duration > actualMaxDuration) {
      const maxFrames = Math.ceil(actualMaxDuration / delay);
      frames = frames.slice(0, maxFrames);
    }

    // 计算缩放比例
    const actualMaxWidth = maxWidth || width;
    const actualMaxHeight = maxHeight || height;
    const scale = Math.min(actualMaxWidth / width, actualMaxHeight / height);

    // 应用缩放
    width = Math.round(width * scale);
    height = Math.round(height * scale);

    // 设置Canvas尺寸
    frameCanvas.width = width;
    frameCanvas.height = height;
    spriteCanvas.width = width * frames.length;
    spriteCanvas.height = height;

    // 优化渲染设置
    [dataCtx, frameCtx, spriteCtx].forEach((ctx) => {
      ctx.imageSmoothingEnabled = scale < 1; // 只在缩小时启用平滑
      ctx.imageSmoothingQuality = "low"; // 使用低质量平滑以提高性能
    });

    // 批量处理帧
    const imageDataCache = new Map<string, ImageData>();

    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i];
      const frameKey = `${frame.dims.width}_${frame.dims.height}`;

      // 复用相同尺寸的ImageData
      let frameImageData = imageDataCache.get(frameKey);
      if (!frameImageData) {
        frameImageData = dataCtx.createImageData(
          frame.dims.width,
          frame.dims.height
        );
        imageDataCache.set(frameKey, frameImageData);
      }

      // 复制帧数据
      frameImageData.data.set(frame.patch);

      // 调整数据Canvas尺寸（仅在必要时）
      if (
        dataCanvas.width !== frame.dims.width ||
        dataCanvas.height !== frame.dims.height
      ) {
        dataCanvas.width = frame.dims.width;
        dataCanvas.height = frame.dims.height;
      }

      dataCtx.putImageData(frameImageData, 0, 0);

      // 处理帧处理类型
      if (frame.disposalType === 2) {
        frameCtx.clearRect(0, 0, width, height);
      }

      // 绘制帧到帧Canvas
      if (scale === 1) {
        // 无缩放时直接绘制
        frameCtx.drawImage(dataCanvas, frame.dims.left, frame.dims.top);
      } else {
        // 有缩放时使用缩放绘制
        frameCtx.drawImage(
          dataCanvas,
          frame.dims.left * scale,
          frame.dims.top * scale,
          frame.dims.width * scale,
          frame.dims.height * scale
        );
      }

      // 添加帧到精灵图
      spriteCtx.drawImage(frameCanvas, width * i, 0);
    }

    // 生成结果
    const result = {
      dataUrl: spriteCanvas.toDataURL("image/png", 0.8), // 使用PNG格式和适中的质量
      frameWidth: width,
      framesLength: frames.length,
      delay,
    };

    // 清理Canvas池
    CanvasPool.returnCanvas(dataCanvas, "data");
    CanvasPool.returnCanvas(frameCanvas, "frame");
    CanvasPool.returnCanvas(spriteCanvas, "sprite");

    // 缓存结果
    gifCache.set(cacheKey, result);

    // 限制缓存大小
    if (gifCache.size > 50) {
      const firstKey = gifCache.keys().next().value;
      gifCache.delete(firstKey);
    }

    return result;
  } catch (processingError) {
    console.error("GIF sprite processing error:", processingError);
    return { error: processingError };
  }
};

// 清理函数，可以在应用关闭时调用
export const cleanupGifCache = () => {
  gifCache.clear();
  CanvasPool.cleanup();
};

// 预热函数，可以在应用启动时调用
export const preloadGifProcessor = () => {
  // 预创建一些常用尺寸的Canvas
  const commonSizes = [
    { width: 100, height: 100 },
    { width: 200, height: 200 },
    { width: 300, height: 300 },
  ];

  commonSizes.forEach((size) => {
    const canvas = CanvasPool.getCanvas("preload", size.width, size.height);
    CanvasPool.returnCanvas(canvas, "preload");
  });
};
