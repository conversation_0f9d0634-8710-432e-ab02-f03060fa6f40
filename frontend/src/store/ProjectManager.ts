import { Store } from "./Store";
import { Animation, Caption, EditorElement, Track, TrackType } from "../types";

// 常量定义
const CONSTANTS = {
  SAVE: {
    AUTO_SAVE_KEY_PREFIX: "fabric-canvas-state",
    DEFAULT_PROJECT_KEY: "fabric-canvas-state-default",
  },
  ANIMATION: {
    DEFAULT_TRANSITION: "fade",
    PRECISION_DIGITS: 2,
  },
  TIMING: {
    FINALIZE_DELAY: 800,
    FALLBACK_DELAY: 300,
  },
} as const;

// 动画类型映射配置
const ANIMATION_TYPE_MAPPING = {
  fadeIn: "fade",
  fadeOut: "fade",
  breathe: "fade",
  bounce: "fade",
  shake: "fade",
  flash: "fade",
  rotate: "circleopen",
  zoom: "circleopen",
  zoomIn: "circleopen",
  zoomOut: "circleopen",
} as const;

// 方向映射配置
const DIRECTION_MAPPING = {
  clipPath: {
    left: "wiperight",
    right: "wipeleft",
    top: "wipedown",
    bottom: "wipeup",
  },
  slide: {
    left: "slideright",
    right: "slideleft",
    top: "slidedown",
    bottom: "slideup",
  },
} as const;

// 类型定义
interface TransitionInfo {
  type: string;
  duration?: number;
}

interface ElementTransition {
  in?: string;
  out?: string;
  inDuration?: number;
  outDuration?: number;
  duration?: number;
}

interface CanvasState {
  backgroundColor: string;
  width: number;
  height: number;
  elements: any[];
  animations: Animation[];
  captions: Caption[];
  globalCaptionStyle: any;
  tracks: Track[];
}

export class ProjectManager {
  private store: Store;
  projectName: string = "Untitled video";
  private _isImporting: boolean = false;

  constructor(store: Store) {
    this.store = store;
  }

  /**
   * 获取当前项目的存储键
   * @returns 项目专用的存储键
   */
  private getProjectStorageKey(): string {
    // 为项目名称生成安全的存储键
    const safeName = this.projectName
      .replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, "_") // 替换特殊字符为下划线
      .substring(0, 50); // 限制长度

    const storageKey = `${CONSTANTS.SAVE.AUTO_SAVE_KEY_PREFIX}-${safeName}`;
    console.log(`🔑 项目 "${this.projectName}" 的存储键: ${storageKey}`);
    return storageKey;
  }

  get isImporting(): boolean {
    return this._isImporting;
  }

  // ==================== 项目管理方法 ====================

  setProjectName(name: string): void {
    this.projectName = name;
    this.store.saveChange();
  }

  // ==================== 动画转换相关方法 ====================

  /**
   * 处理单个动画组的转换
   * @param elementAnimations 元素的所有动画
   * @param group 动画组类型（入场或出场）
   * @param elementId 元素ID
   * @returns 转换信息
   */
  private _processAnimationForTransition(
    elementAnimations: Animation[],
    group: "in" | "out",
    elementId: string
  ): TransitionInfo {
    const animation = elementAnimations.find((anim) => anim.group === group);

    if (!animation) {
      return { type: "none" };
    }

    const duration = animation.duration / 1000;
    const groupText = group === "in" ? "入场" : "出场";

    console.log(
      `🎬 ${groupText}动画 - 元素 ${elementId}: 类型=${animation.type}, 时间=${duration}秒`
    );

    return {
      type: this.mapAnimationTypeToXfadeTransition(
        animation.type,
        animation.properties
      ),
      duration,
    };
  }

  /**
   * 将前端动画类型映射到FFmpeg xfade transition类型
   * @param animationType 前端动画类型
   * @param properties 动画属性
   * @returns FFmpeg xfade transition类型
   */
  private mapAnimationTypeToXfadeTransition(
    animationType: string,
    properties?: any
  ): string {
    // 处理简单映射
    if (animationType in ANIMATION_TYPE_MAPPING) {
      return ANIMATION_TYPE_MAPPING[
        animationType as keyof typeof ANIMATION_TYPE_MAPPING
      ];
    }

    // 处理复杂的slide动画
    if (animationType === "slideIn" || animationType === "slideOut") {
      return this._mapSlideAnimation(properties);
    }

    // 默认返回fade
    return CONSTANTS.ANIMATION.DEFAULT_TRANSITION;
  }

  /**
   * 映射slide动画类型
   * @param properties 动画属性
   * @returns 对应的transition类型
   */
  private _mapSlideAnimation(properties?: any): string {
    const useClipPath = properties?.useClipPath;
    const direction = properties?.direction;

    if (!direction) {
      return CONSTANTS.ANIMATION.DEFAULT_TRANSITION;
    }

    const mappingType = useClipPath ? "clipPath" : "slide";
    const directionMap = DIRECTION_MAPPING[mappingType];

    return (
      directionMap[direction as keyof typeof directionMap] ||
      CONSTANTS.ANIMATION.DEFAULT_TRANSITION
    );
  }

  /**
   * 将元素动画转换为transition配置
   * @param elementId 元素ID
   * @returns transition配置对象
   */
  private convertAnimationsToTransitions(elementId: string): ElementTransition {
    const elementAnimations = this.store.animations.filter(
      (animation) => animation.targetId === elementId
    );

    const transition: ElementTransition = {};

    // 处理入场动画
    const inTransition = this._processAnimationForTransition(
      elementAnimations,
      "in",
      elementId
    );
    transition.in = inTransition.type;
    if (inTransition.duration) {
      transition.inDuration = inTransition.duration;
    }

    // 处理出场动画
    const outTransition = this._processAnimationForTransition(
      elementAnimations,
      "out",
      elementId
    );
    transition.out = outTransition.type;
    if (outTransition.duration) {
      transition.outDuration = outTransition.duration;
    }

    // 设置总时长
    this._setTransitionDuration(transition);

    return transition;
  }

  /**
   * 设置transition的总时长
   * @param transition transition对象
   */
  private _setTransitionDuration(transition: ElementTransition): void {
    const { inDuration, outDuration } = transition;

    if (inDuration && !outDuration) {
      transition.duration = inDuration;
    } else if (outDuration && !inDuration) {
      transition.duration = outDuration;
    } else if (inDuration && outDuration) {
      transition.duration = inDuration;
    }
  }

  // ==================== 导出相关方法 ====================

  /**
   * 导出画布状态
   * @returns JSON字符串格式的画布状态
   */
  exportCanvasState(): string | null {
    if (!this.store.canvas) {
      return null;
    }

    const canvasState: CanvasState = {
      backgroundColor: this.store.backgroundColor,
      width: this.store.canvasWidth,
      height: this.store.canvasHeight,
      elements: this._processElementsForExport(),
      animations: this.store.animations,
      captions: this.store.captions,
      globalCaptionStyle: this.store.captionManager.globalCaptionStyle,
      tracks: this.store.trackManager.tracks,
    };

    console.log("导出的画布状态（包含transition信息）:", canvasState);
    return JSON.stringify(canvasState);
  }

  /**
   * 处理元素数据用于导出
   * @returns 处理后的元素数组
   */
  private _processElementsForExport(): any[] {
    return this.store.editorElements.map(({ fabricObject, ...element }) => {
      const transition = this.convertAnimationsToTransitions(element.id);

      // 调试日志
      this._logAnimationDebugInfo(element.id);

      return {
        ...element,
        transition,
        placement: this._processPlacementForExport(element.placement),
        timeFrame: this._processTimeFrameForExport(element.timeFrame),
      };
    });
  }

  /**
   * 处理位置信息用于导出
   * @param placement 原始位置信息
   * @returns 处理后的位置信息
   */
  private _processPlacementForExport(placement: any): any {
    if (!placement) return null;

    const precision = CONSTANTS.ANIMATION.PRECISION_DIGITS;
    return {
      ...placement,
      x: placement.x ? Number(placement.x.toFixed(precision)) : 0,
      y: placement.y ? Number(placement.y.toFixed(precision)) : 0,
      width: placement.width ? Number(placement.width.toFixed(precision)) : 0,
      height: placement.height
        ? Number(placement.height.toFixed(precision))
        : 0,
    };
  }

  /**
   * 处理时间帧信息用于导出
   * @param timeFrame 原始时间帧信息
   * @returns 处理后的时间帧信息
   */
  private _processTimeFrameForExport(timeFrame: any): any {
    const precision = CONSTANTS.ANIMATION.PRECISION_DIGITS;
    return {
      start: Number(timeFrame.start.toFixed(precision)),
      end: Number(timeFrame.end.toFixed(precision)),
    };
  }

  /**
   * 记录动画调试信息
   * @param elementId 元素ID
   */
  private _logAnimationDebugInfo(elementId: string): void {
    const elementAnimations = this.store.animations.filter(
      (animation) => animation.targetId === elementId
    );

    if (elementAnimations.length > 0) {
      console.log(`🔍 动画调试 - 元素 ${elementId}:`);
      console.log("原始动画:", elementAnimations);
      console.log(
        "转换后的transition:",
        this.convertAnimationsToTransitions(elementId)
      );
    }
  }

  // ==================== 导入相关方法 ====================

  /**
   * 导入画布状态
   * @param jsonState JSON格式的状态字符串
   * @param needLoading 是否需要显示加载状态，默认为true
   * @returns 是否导入成功
   */
  importCanvasState(jsonState: string, needLoading: boolean = true): boolean {
    try {
      this._isImporting = true;
      if (needLoading) {
        this.store.setLoading(true, "loading project data...");
      }

      const canvasData = JSON.parse(jsonState);

      this._resetStateForImport();
      this._importCanvasSettings(canvasData);
      this._importCaptions(canvasData);
      this._importTracks(canvasData);

      this._importElementsAndMedia(canvasData, needLoading).then(async () => {
        this._importAnimations(canvasData);
        await this._finalizeImport(needLoading);
        this._isImporting = false;
      });

      return true;
    } catch (error) {
      console.error("Error importing canvas state:", error);
      this._isImporting = false;
      if (needLoading) {
        this.store.setLoading(false, "");
      }
      return false;
    }
  }

  /**
   * 重置状态准备导入
   */
  private _resetStateForImport(): void {
    this.store.historyManager.clearHistory();
    this.store.setEditorElements([]);
    this.store.animations = [];
    this.store.trackManager.tracks = [];
    this.store.trackManager.defaultTracks = {
      media: "",
      audio: "",
      text: "",
      caption: "",
    };
  }

  /**
   * 导入画布设置
   * @param canvasData 画布数据
   */
  private _importCanvasSettings(canvasData: any): void {
    this.store.setCanvasSize(canvasData.width, canvasData.height);
    this.store.setBackgroundColor(canvasData.backgroundColor);
  }

  /**
   * 导入字幕数据
   * @param canvasData 画布数据
   */
  private _importCaptions(canvasData: any): void {
    if (canvasData.captions) {
      this.store.captionManager.setCaptions(canvasData.captions);
      this.store.captions = this.store.captionManager.captions;
    }

    if (canvasData.globalCaptionStyle) {
      this.store.captionManager.globalCaptionStyle =
        canvasData.globalCaptionStyle;
    }
  }

  /**
   * 导入轨道数据
   * @param canvasData 画布数据
   */
  private _importTracks(canvasData: any): void {
    if (canvasData.tracks) {
      this.store.trackManager.tracks = canvasData.tracks;
    }

    if (canvasData.defaultTracks) {
      this.store.trackManager.defaultTracks = canvasData.defaultTracks;
    }
  }

  /**
   * 导入动画数据
   * @param canvasData 画布数据
   */
  private _importAnimations(canvasData: any): void {
    if (canvasData.animations) {
      this.store.animations = canvasData.animations;
    }
  }

  /**
   * 异步创建元素
   * @param element 元素数据
   * @returns Promise
   */
  private _createElementAsync(element: any): Promise<void> {
    return new Promise((resolve) => {
      const elementCreators = this._getElementCreators(element, resolve);
      const creator = elementCreators[element.type];

      if (creator) {
        creator();
      } else {
        resolve();
      }
    });
  }

  /**
   * 获取元素创建器映射
   * @param element 元素数据
   * @param resolve Promise resolve函数
   * @returns 创建器映射对象
   */
  private _getElementCreators(
    element: any,
    resolve: () => void
  ): Record<string, () => void> {
    const addElementAndResolve = () => {
      this.store.setEditorElements([element, ...this.store.editorElements]);
      this.store.elementManager.addElement(element);
      resolve();
    };

    return {
      text: addElementAndResolve,
      shape: addElementAndResolve,
      image: () => this._createImageElement(element, resolve),
      gif: () => this._createGifElement(element, resolve),
      video: () => this._createVideoElement(element, resolve),
      audio: () => this._createAudioElement(element, resolve),
    };
  }

  /**
   * 创建图片元素
   * @param element 元素数据
   * @param resolve Promise resolve函数
   */
  private _createImageElement(element: any, resolve: () => void): void {
    const imageElement = document.createElement("img");
    imageElement.src = element.properties.src;
    imageElement.id = element.properties.elementId; // 使用保存的elementId，而不是image-${element.id}
    imageElement.style.display = "none";
    document.body.appendChild(imageElement);

    imageElement.onload = () => {
      // 在项目加载时，只添加到数组，fabric对象由refreshElements统一创建
      this.store.setEditorElements([element, ...this.store.editorElements]);
      resolve();
    };

    imageElement.onerror = () => resolve();
  }

  /**
   * 创建GIF元素
   * @param element 元素数据
   * @param resolve Promise resolve函数
   */
  private _createGifElement(element: any, resolve: () => void): void {
    const gifElement = document.createElement("img");
    gifElement.src = element.properties.src;
    gifElement.id = element.properties.elementId; // 使用保存的elementId，而不是gif-${element.id}
    gifElement.style.display = "none";
    gifElement.crossOrigin = "anonymous";
    document.body.appendChild(gifElement);

    gifElement.onload = () => {
      // 在项目加载时，只添加到数组，fabric对象由refreshElements统一创建
      this.store.setEditorElements([element, ...this.store.editorElements]);
      resolve();
    };

    gifElement.onerror = () => resolve();
  }

  /**
   * 创建视频元素
   * @param element 元素数据
   * @param resolve Promise resolve函数
   */
  private _createVideoElement(element: any, resolve: () => void): void {
    const videoElement = document.createElement("video");
    videoElement.style.display = "none";
    videoElement.src = element.properties.src;
    videoElement.id = element.properties.elementId; // 使用保存的elementId

    videoElement.onloadeddata = () => {
      // 在项目加载时，只添加到数组，fabric对象由refreshElements统一创建
      this.store.setEditorElements([element, ...this.store.editorElements]);
      resolve();
    };

    videoElement.onerror = () => resolve();
    document.body.appendChild(videoElement);
  }

  /**
   * 创建音频元素
   * @param element 元素数据
   * @param resolve Promise resolve函数
   */
  private _createAudioElement(element: any, resolve: () => void): void {
    const audio = document.createElement("audio");
    audio.src = element.properties.src;
    audio.id = element.properties.elementId; // 使用保存的elementId

    audio.onloadeddata = () => {
      this.store.setEditorElements([element, ...this.store.editorElements]);
      resolve();
    };

    audio.onerror = () => resolve();
    document.body.appendChild(audio);
  }

  /**
   * 从元素重建轨道
   */
  private _reconstructTracksFromElements(): void {
    this.store.trackManager.tracks = [];
    const trackIds = new Set<string>();

    // 收集所有轨道ID
    this.store.editorElements.forEach((element) => {
      if (element.trackId) {
        trackIds.add(element.trackId);
      }
    });

    // 为每个轨道ID创建轨道
    trackIds.forEach((trackId) => {
      const track = this._createTrackFromElements(trackId);
      if (track) {
        this.store.trackManager.tracks.push(track);
        this._updateDefaultTrack(track);
      }
    });
  }

  /**
   * 从元素创建轨道
   * @param trackId 轨道ID
   * @returns 创建的轨道或null
   */
  private _createTrackFromElements(trackId: string): Track | null {
    const elementsWithTrackId = this.store.editorElements.filter(
      (element) => element.trackId === trackId
    );

    if (elementsWithTrackId.length === 0) {
      return null;
    }

    const firstElement = elementsWithTrackId[0];
    const trackType = this._determineTrackType(firstElement);

    return {
      id: trackId,
      name: `${trackType.charAt(0).toUpperCase() + trackType.slice(1)} Track`,
      type: trackType,
      elementIds: elementsWithTrackId.map((element) => element.id),
      isVisible: true,
      isLocked: false,
    };
  }

  /**
   * 确定轨道类型
   * @param element 元素
   * @returns 轨道类型
   */
  private _determineTrackType(element: EditorElement): TrackType {
    if (element.type === "image" || element.type === "video") {
      return "media";
    }
    return element.type as TrackType;
  }

  /**
   * 更新默认轨道
   * @param track 轨道
   */
  private _updateDefaultTrack(track: Track): void {
    if (!this.store.trackManager.defaultTracks[track.type]) {
      this.store.trackManager.defaultTracks[track.type] = track.id;
    }
  }

  /**
   * 导入元素和媒体
   * @param canvasData 画布数据
   * @param needLoading 是否需要显示加载状态
   */
  private async _importElementsAndMedia(
    canvasData: any,
    needLoading: boolean = true
  ): Promise<void> {
    if (needLoading) {
      this.store.setLoading(true, "loading elements...");
    }

    const elementsMap = new Map<string, any>(
      canvasData.elements.map((element: any) => [element.id, element])
    );

    // 逆序创建元素
    for (const element of [...canvasData.elements].reverse()) {
      const elementWithDefaults = this._addElementDefaults(element);
      await this._createElementAsync(elementWithDefaults);
    }

    // 更新元素属性（直接更新现有元素，避免重复设置）
    this._updateElementsPropertiesInPlace(elementsMap);

    // 处理轨道
    this._handleTracksAfterImport(canvasData);
  }

  /**
   * 为元素添加默认值
   * @param element 原始元素
   * @returns 带默认值的元素
   */
  private _addElementDefaults(element: any): any {
    return {
      ...element,
      locked: element.locked ?? false,
      opacity: element.opacity ?? 1,
    };
  }

  /**
   * 直接更新现有元素的属性，避免重复设置editorElements
   * @param elementsMap 元素映射
   */
  private _updateElementsPropertiesInPlace(
    elementsMap: Map<string, any>
  ): void {
    const currentElements = this.store.editorElements;
    for (let i = 0; i < currentElements.length; i++) {
      const newElement = currentElements[i];
      const originalElement = elementsMap.get(newElement.id);

      if (newElement && originalElement) {
        // 直接更新元素属性
        currentElements[i] = {
          ...newElement,
          placement: originalElement.placement,
          timeFrame: originalElement.timeFrame,
          properties: {
            ...newElement.properties,
            ...originalElement.properties,
          },
          trackId: originalElement.trackId,
          locked: originalElement.locked ?? newElement.locked ?? false,
          opacity: originalElement.opacity ?? newElement.opacity ?? 1,
        };
      }
    }
    // 触发响应式更新
    this.store.setEditorElements([...currentElements]);
  }

  /**
   * 从原始数据更新元素
   * @param elementsMap 元素映射
   * @returns 更新后的元素数组
   */
  private _updateElementsFromOriginal(
    elementsMap: Map<string, any>
  ): EditorElement[] {
    return this.store.editorElements.map((newElement) => {
      const originalElement = elementsMap.get(newElement.id);

      if (!newElement || !originalElement) {
        return newElement;
      }

      return {
        ...newElement,
        placement: originalElement.placement,
        timeFrame: originalElement.timeFrame,
        properties: {
          ...newElement.properties,
          ...originalElement.properties,
        },
        trackId: originalElement.trackId,
        locked: originalElement.locked ?? newElement.locked ?? false,
        opacity: originalElement.opacity ?? newElement.opacity ?? 1,
      };
    });
  }

  /**
   * 导入后处理轨道
   * @param canvasData 画布数据
   */
  private _handleTracksAfterImport(canvasData: any): void {
    if (!canvasData.tracks) {
      this._reconstructTracksFromElements();
    } else {
      this._syncElementsWithTracks();
    }
  }

  /**
   * 同步元素与轨道
   */
  private _syncElementsWithTracks(): void {
    this.store.editorElements.forEach((element) => {
      if (!element.trackId) return;

      const track = this.store.trackManager.tracks.find(
        (t) => t.id === element.trackId
      );

      if (track && !track.elementIds.includes(element.id)) {
        track.elementIds.push(element.id);
      }
    });
  }

  /**
   * 完成导入流程
   * @param needLoading 是否需要显示加载状态
   */
  private async _finalizeImport(needLoading: boolean = true): Promise<void> {
    if (needLoading) {
      this.store.setLoading(true, "completing import...");
    }

    // 刷新和清理
    await this.store.refreshElements();
    this.store.refreshAnimations();
    this.store.trackManager.cleanupInvalidElementIds();
    this.store.updateCanvasOrderByTrackOrder();
    this.store.updateMaxTime();
    this.store.fitTimelineToContent();
    this.store.historyManager.initHistory();

    // 延迟完成加载
    setTimeout(() => {
      if (this._areAllElementsLoaded()) {
        if (needLoading) {
          this.store.setLoading(false, "");
        }
        console.log("所有元素加载完成，UI可以安全渲染");
      } else {
        setTimeout(() => {
          if (needLoading) {
            this.store.setLoading(false, "");
          }
        }, CONSTANTS.TIMING.FALLBACK_DELAY);
      }
    }, CONSTANTS.TIMING.FINALIZE_DELAY);
  }

  /**
   * 检查所有元素是否已加载
   * @returns 是否全部加载完成
   */
  private _areAllElementsLoaded(): boolean {
    return this.store.editorElements.every((element) => {
      if (element.type === "text") {
        return (
          element.properties &&
          element.properties.fontSize !== undefined &&
          element.properties.fontColor !== undefined
        );
      }
      return true;
    });
  }

  // ==================== 本地存储相关方法 ====================

  /**
   * 生成项目缩略图
   * @returns 缩略图的数据URL，如果生成失败则返回undefined
   */
  generateProjectThumbnail(): string | undefined {
    if (!this.store.canvas) return undefined;

    try {
      // 直接从Fabric.js canvas生成缩略图
      const canvasDataURL = this.store.canvas.toDataURL({
        format: "jpeg",
        quality: 0.8,
        multiplier: 0.25, // 缩小到原尺寸的25%作为缩略图
      });

      return canvasDataURL;
    } catch (error) {
      console.error("生成缩略图失败:", error);
      return undefined;
    }
  }

  /**
   * 保存到本地存储
   */
  saveToLocalStorage(): void {
    try {
      // 清理无效数据
      const removedCount = this.store.trackManager.cleanupInvalidElementIds();
      if (removedCount > 0) {
        console.log(`已从轨道中清理 ${removedCount} 个无效的元素ID`);
      }

      this.store.trackManager.removeEmptyTracks();

      // 导出并保存到项目专用的存储键
      const state = this.exportCanvasState();
      if (state) {
        const storageKey = this.getProjectStorageKey();
        localStorage.setItem(storageKey, state);
        console.log(
          `项目 "${this.projectName}" 已保存到本地存储，键: ${storageKey}`
        );
      }
    } catch (error) {
      console.error("Failed to save canvas state to localStorage:", error);
    }
  }

  /**
   * 保存项目到最近项目列表
   */
  saveToRecentProjects(): void {
    try {
      const RECENT_PROJECTS_KEY = "fabric-recent-projects";
      const MAX_RECENT_PROJECTS = 10;

      // 获取当前项目状态
      const canvasState = this.exportCanvasState();
      if (!canvasState) {
        console.warn("无法导出画布状态，跳过保存到最近项目");
        return;
      }

      // 生成缩略图
      const thumbnail = this.generateProjectThumbnail();

      // 计算项目统计信息
      const elementCount = this.store.editorElements.length;
      const duration = this.store.timelineManager.maxTime;

      // 创建项目元数据
      const projectMetadata = {
        id: `project_${Date.now()}`,
        name: this.projectName,
        lastModified: new Date(),
        duration,
        elementCount,
        thumbnail,
        canvasState,
      };

      // 获取现有的最近项目列表
      const existingProjects = JSON.parse(
        localStorage.getItem(RECENT_PROJECTS_KEY) || "[]"
      );

      // 移除同名的旧项目（如果存在）
      const filteredProjects = existingProjects.filter(
        (p: any) => p.name !== this.projectName
      );

      // 添加新项目到列表开头
      const updatedProjects = [projectMetadata, ...filteredProjects].slice(
        0,
        MAX_RECENT_PROJECTS
      );

      // 保存到本地存储
      localStorage.setItem(
        RECENT_PROJECTS_KEY,
        JSON.stringify(updatedProjects)
      );

      console.log(`项目 "${this.projectName}" 已保存到最近项目列表`);
    } catch (error) {
      console.error("保存到最近项目列表失败:", error);
    }
  }

  /**
   * 从本地存储加载
   * @returns 是否加载成功
   */
  loadFromLocalStorage(): boolean {
    this.store.setLoading(true, "正在从本地存储加载数据...");

    const storageKey = this.getProjectStorageKey();
    const savedState = localStorage.getItem(storageKey);

    if (!savedState) {
      console.log(
        `ℹ️ 没有找到项目 "${this.projectName}" 的本地存储数据，键: ${storageKey}`
      );
      this.store.setLoading(false, "");
      return false;
    }

    console.log(`🔄 开始从本地存储加载项目 "${this.projectName}" 的数据`);
    const result = this.importCanvasState(savedState);

    // 确保更新最大时间
    this.store.updateMaxTime();

    console.log("✅ 本地存储数据加载完成，结果:", result);
    return result;
  }

  // ==================== 视频导出方法 ====================

  /**
   * 导出视频
   * @param format 视频格式，默认为mp4
   * @returns 响应数据
   */
  async exportVideo(format: string = "mp4"): Promise<any> {
    console.log("exportVideo", format);

    const canvasState = this.exportCanvasState();
    if (!canvasState) {
      throw new Error("无法导出画布状态");
    }

    console.log("canvasState", canvasState);

    const response = await fetch("http://localhost:8080/api/generateVideo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: canvasState,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to generate video");
    }

    return await response.json();
  }
}
