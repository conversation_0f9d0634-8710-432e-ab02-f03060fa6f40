import { fabric } from "fabric";
import { makeAutoObservable } from "mobx";
import { Store } from "./Store";

const CONSTANTS = {
  CANVAS: {
    MIN_SCALE: 0.1,
    MAX_SCALE: 2,
  },
};

export class CanvasManager {
  private store: Store;

  editMode: "move" | "hand" = "move";
  canvasScale: number = 0.2;
  canvasTranslation: { x: number; y: number } = { x: 0, y: 0 };

  _pan: {
    enable: boolean;
    isDragging: boolean;
    lastPosX: number;
    lastPosY: number;
  };

  constructor(store: Store) {
    this.store = store;
    this._pan = {
      enable: false,
      isDragging: false,
      lastPosX: 0,
      lastPosY: 0,
    };
    makeAutoObservable(this);
  }

  get canvas(): fabric.Canvas | null {
    return this.store.canvas;
  }

  /**
   * 设置编辑模式
   * @param mode 编辑模式：'move'或'hand'
   */
  setEditMode(mode: "move" | "hand") {
    // 如果模式没有变化，不做任何操作
    if (this.editMode === mode) return;

    // 保存旧模式，用于日志记录
    const oldMode = this.editMode;
    console.log(`切换编辑模式: ${oldMode} -> ${mode}`);
    this.editMode = mode;

    if (!this.canvas) return;

    // 如果切换到手工具模式，禁用所有对象的可选择性和可移动性
    if (mode === "hand") {
      this.canvas.discardActiveObject();
      this.store.setSelectedElement(null);

      // 禁用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = false;
        obj.evented = false; // 禁止所有事件交互
      });

      this.canvas.requestRenderAll();
    } else {
      // 当切换回移动模式时，启用所有对象的可选择性和可移动性
      this.canvas.getObjects().forEach((obj) => {
        obj.selectable = true;
        obj.evented = true; // 恢复事件交互
      });

      this.canvas.requestRenderAll();
    }
  }

  /**
   * 更新画布缩放值
   * @param scale 新的缩放值
   * @param translation 新的平移值（可选）
   */
  updateCanvasScale(scale: number, translation?: { x: number; y: number }) {
    this.canvasScale = this._clampScale(scale);

    if (translation) {
      this.canvasTranslation = translation;
    }
  }

  private _clampScale(scale: number): number {
    return Math.max(
      CONSTANTS.CANVAS.MIN_SCALE,
      Math.min(CONSTANTS.CANVAS.MAX_SCALE, scale)
    );
  }

  /**
   * 放大画布
   * @param step 缩放步长，默认为0.1
   */
  zoomIn(step: number = 0.1) {
    const newScale = this.canvasScale + step;
    this.updateCanvasScale(newScale);
    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 缩小画布
   * @param step 缩放步长，默认为0.1
   */
  zoomOut(step: number = 0.1) {
    const newScale = this.canvasScale - step;
    this.updateCanvasScale(newScale);
    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }

  /**
   * 重置画布缩放
   */
  resetZoom() {
    // 获取窗口尺寸
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 使用store中的实际canvas尺寸进行计算
    const canvasWidth = this.store.canvasWidth;
    const canvasHeight = this.store.canvasHeight;

    // 计算适合窗口的缩放值，同时保持宽高比
    const scaleX = (windowWidth * 0.7) / canvasWidth;
    const scaleY = (windowHeight * 0.7) / canvasHeight;
    const scale = Math.min(scaleX, scaleY);

    // 居中画布
    const x = windowWidth / 2;
    const y = (windowHeight - 260) / 2; // 考虑UI元素（260px）

    this.updateCanvasScale(scale, { x, y });
    return {
      scale: this.canvasScale,
      translation: this.canvasTranslation,
    };
  }
}
