import { makeObservable, observable, action } from "mobx";
import { fabric } from "fabric";
import { Store } from "./Store";
import { Track, TrackType } from "../types";

// 历史记录操作类型
export type HistoryActionType =
  | "元素添加"
  | "元素删除"
  | "元素修改"
  | "元素移动"
  | "元素调整大小"
  | "元素旋转"
  | "元素属性修改"
  | "画布调整"
  | "字幕修改"
  | "轨道修改"
  | "初始状态"
  | "其他操作";

// 历史记录状态接口
export interface HistoryState {
  editorElements: any[];
  animations: any[]; // 添加动画状态
  canvasWidth: number;
  canvasHeight: number;
  backgroundColor: string;
  captions: any[];
  tracks: Track[];
  defaultTracks: Record<TrackType, string>;
  actionType: HistoryActionType;
  timestamp: number;
}

export class HistoryManager {
  private store: Store;
  history: HistoryState[] = [];
  currentHistoryIndex: number = -1;
  maxHistorySteps: number = 50;

  // 操作分组相关
  isGrouping: boolean = false; // 公开属性，允许外部检查分组状态
  private groupActionType: HistoryActionType | null = null;
  private groupingTimeout: NodeJS.Timeout | null = null;
  private readonly GROUP_TIMEOUT = 1000; // 1秒内的连续操作会被分组

  // 是否正在应用历史记录状态（防止在undo/redo过程中触发新的历史记录保存）
  private isApplyingHistory: boolean = false;

  constructor(store: Store) {
    this.store = store;
    // 使用makeObservable代替makeAutoObservable，这样我们可以明确指定哪些属性是可观察的
    makeObservable(this, {
      // 可观察的属性
      history: observable,
      currentHistoryIndex: observable,
      maxHistorySteps: observable,
      isGrouping: observable,

      // 动作方法
      saveToHistory: action,
      undo: action,
      redo: action,
      startGrouping: action,
      endGrouping: action,
      clearHistory: action,
      initHistory: action,
    });
  }

  /**
   * 开始操作分组
   * @param actionType 操作类型
   */
  startGrouping(actionType: HistoryActionType) {
    this.isGrouping = true;
    this.groupActionType = actionType;

    // 清除之前的超时
    if (this.groupingTimeout) {
      clearTimeout(this.groupingTimeout);
    }

    // 设置新的超时，超时后自动结束分组
    this.groupingTimeout = setTimeout(() => {
      this.endGrouping();
    }, this.GROUP_TIMEOUT);
  }

  /**
   * 结束操作分组
   */
  endGrouping() {
    this.isGrouping = false;
    this.groupActionType = null;

    if (this.groupingTimeout) {
      clearTimeout(this.groupingTimeout);
      this.groupingTimeout = null;
    }
  }

  /**
   * 检查是否正在应用历史记录状态
   * @returns 是否正在应用历史记录状态
   */
  get isApplyingHistoryState(): boolean {
    return this.isApplyingHistory;
  }

  /**
   * 保存当前状态到历史记录
   * @param actionType 操作类型，默认为"其他操作"
   */
  saveToHistory(actionType: HistoryActionType = "其他操作") {
    // 如果正在分组中，并且不是第一个操作，则使用分组的操作类型
    if (this.isGrouping && this.history.length > 0 && this.groupActionType) {
      // 如果是同一个分组的操作，则替换最后一个历史记录
      if (this.currentHistoryIndex === this.history.length - 1) {
        actionType = this.groupActionType;

        // 更新最后一个历史记录
        this.history[this.currentHistoryIndex] =
          this.createHistoryState(actionType);
        return;
      }
    }

    // 如果当前不在历史记录的末尾，则删除后面的历史记录
    if (this.currentHistoryIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentHistoryIndex + 1);
    }

    // 创建新的历史记录
    const currentState = this.createHistoryState(actionType);

    // 添加到历史记录
    this.history.push(currentState);
    this.currentHistoryIndex++;

    // 限制历史记录大小
    if (this.history.length > this.maxHistorySteps) {
      this.history.shift();
      this.currentHistoryIndex--;
    }
  }

  /**
   * 创建历史记录状态对象
   * @param actionType 操作类型
   * @returns 历史记录状态对象
   */
  private createHistoryState(actionType: HistoryActionType): HistoryState {
    // 获取当前canvas的背景色，确保使用最新的背景色
    let backgroundColor = this.store.backgroundColor;
    if (this.store.canvas) {
      // 如果canvas存在，使用canvas的背景色
      const canvasBackground = this.store.canvas.backgroundColor;
      if (canvasBackground) {
        // 检查是否是渐变对象
        if (
          typeof canvasBackground === "object" &&
          "colorStops" in canvasBackground &&
          Array.isArray(canvasBackground.colorStops)
        ) {
          // 是渐变对象，保持原有的渐变字符串格式
          backgroundColor = this.store.backgroundColor;
        } else {
          // 普通颜色
          backgroundColor = canvasBackground.toString();
        }
      }
    }

    return {
      editorElements: JSON.parse(JSON.stringify(this.store.editorElements)),
      animations: JSON.parse(JSON.stringify(this.store.animations)), // 保存动画状态
      canvasWidth: this.store.canvasWidth,
      canvasHeight: this.store.canvasHeight,
      backgroundColor: backgroundColor,
      captions: JSON.parse(JSON.stringify(this.store.captionManager.captions)),
      tracks: JSON.parse(JSON.stringify(this.store.trackManager.tracks)),
      defaultTracks: JSON.parse(
        JSON.stringify(this.store.trackManager.defaultTracks)
      ),
      actionType: actionType,
      timestamp: Date.now(),
    };
  }

  /**
   * 撤销操作
   * @returns 是否成功撤销
   */
  undo(): boolean {
    if (this.currentHistoryIndex > 0) {
      this.currentHistoryIndex--;
      const previousState = this.history[this.currentHistoryIndex];

      // 设置标志，防止在应用历史状态时触发新的历史记录保存
      this.isApplyingHistory = true;
      try {
        // 应用历史状态
        this.applyHistoryState(previousState);
        return true;
      } finally {
        // 确保标志被重置
        this.isApplyingHistory = false;
      }
    }
    return false;
  }

  /**
   * 重做操作
   * @returns 是否成功重做
   */
  redo(): boolean {
    if (this.currentHistoryIndex < this.history.length - 1) {
      this.currentHistoryIndex++;
      const nextState = this.history[this.currentHistoryIndex];

      // 设置标志，防止在应用历史状态时触发新的历史记录保存
      this.isApplyingHistory = true;
      try {
        // 应用历史状态
        this.applyHistoryState(nextState);
        return true;
      } finally {
        // 确保标志被重置
        this.isApplyingHistory = false;
      }
    }
    return false;
  }

  /**
   * 应用历史状态
   * @param state 历史状态
   */
  private applyHistoryState(state: HistoryState) {
    console.log(
      `Applying history state: ${state.actionType}, elements: ${state.editorElements.length}`
    );

    // 恢复元素（但不包含fabricObject属性，这些将在refreshElements中重新创建）
    const elements = JSON.parse(JSON.stringify(state.editorElements));

    // 确保所有元素的fabricObject属性为null，以便在refreshElements中重新创建
    elements.forEach((element: any) => {
      element.fabricObject = null;
    });

    // 先恢复媒体元素到DOM中
    this.restoreMediaElements(elements);

    // 然后设置编辑器元素
    this.store.editorElements = elements;

    // 恢复动画状态
    if (state.animations) {
      this.store.animations = JSON.parse(JSON.stringify(state.animations));
    } else {
      // 如果历史状态中没有动画数据（向后兼容），清空动画
      this.store.animations = [];
    }

    // 恢复画布属性
    this.store.canvasWidth = state.canvasWidth;
    this.store.canvasHeight = state.canvasHeight;
    this.store.backgroundColor = state.backgroundColor;

    // 恢复字幕（直接设置，不触发历史记录保存）
    if (state.captions) {
      this.store.captionManager.captions = JSON.parse(
        JSON.stringify(state.captions)
      );
      this.store.captions = this.store.captionManager.captions;

      // 如果字幕为空，重置样式
      if (state.captions.length === 0) {
        this.store.captionManager.resetGlobalCaptionStyle();

        // 移除画布中的字幕对象
        if (this.store.captionManager.captionTextObject && this.store.canvas) {
          this.store.canvas.remove(this.store.captionManager.captionTextObject);
          this.store.captionManager.captionTextObject = null;
        }
      }
    }

    // 恢复轨道
    if (state.tracks) {
      this.store.trackManager.tracks = JSON.parse(JSON.stringify(state.tracks));
    }

    // 恢复默认轨道
    if (state.defaultTracks) {
      this.store.trackManager.defaultTracks = JSON.parse(
        JSON.stringify(state.defaultTracks)
      );
    }

    // 更新画布（直接设置，不触发历史记录保存）
    if (this.store.canvas) {
      this.store.canvas.setWidth(this.store.canvasWidth);
      this.store.canvas.setHeight(this.store.canvasHeight);

      // 设置背景色
      if (this.store.backgroundColor.startsWith("linear-gradient")) {
        const gradientObj = new fabric.Gradient({
          type: "linear",
          coords: { x1: 0, y1: 0, x2: this.store.canvas.width, y2: 0 },
          colorStops: [
            {
              offset: 0,
              color: this.store.backgroundColor.match(/#[a-f\d]{6}/gi)[0],
            },
            {
              offset: 1,
              color: this.store.backgroundColor.match(/#[a-f\d]{6}/gi)[1],
            },
          ],
        });
        this.store.canvas.setBackgroundColor(
          gradientObj,
          this.store.canvas.renderAll.bind(this.store.canvas)
        );
      } else {
        this.store.canvas.backgroundColor = this.store.backgroundColor;
        this.store.canvas.renderAll();
      }
    }

    // 刷新元素和动画
    this.store.refreshElements();
    this.store.refreshAnimations();

    // 清理无效的元素ID并删除空轨道
    // 这在撤销/重做操作后特别重要，因为可能有元素被删除或恢复
    this.store.trackManager.cleanupInvalidElementIds();

    // 清除选中状态
    this.store.setSelectedElement(null);
    this.store.deselectAllCaptions();

    // 确保画布重新渲染
    if (this.store.canvas) {
      this.store.canvas.requestRenderAll();
    }
  }

  /**
   * 恢复媒体元素到DOM中
   * @param elements 编辑器元素数组
   */
  private restoreMediaElements(elements: any[]) {
    elements.forEach((element) => {
      if (
        element.type === "video" ||
        element.type === "audio" ||
        element.type === "image"
      ) {
        const elementId = element.properties?.elementId;
        if (!elementId) return;

        // 检查DOM中是否已存在该元素
        const existingElement = document.getElementById(elementId);
        if (existingElement) return; // 如果已存在，不需要重新创建

        console.log(
          `Restoring media element: ${elementId}, type: ${element.type}`
        );

        if (element.type === "video") {
          // 创建视频元素
          const videoElement = document.createElement("video");
          videoElement.id = elementId;
          videoElement.src = element.properties.src;
          videoElement.crossOrigin = "anonymous";
          videoElement.style.display = "none";
          document.body.appendChild(videoElement);

          // 设置视频属性
          if (element.properties.mediaStartTime) {
            videoElement.currentTime = element.properties.mediaStartTime;
          }
        } else if (element.type === "audio") {
          // 创建音频元素
          const audioElement = document.createElement("audio");
          audioElement.id = elementId;
          audioElement.src = element.properties.src;
          audioElement.crossOrigin = "anonymous";
          audioElement.style.display = "none";
          document.body.appendChild(audioElement);

          // 设置音频属性
          if (element.properties.mediaStartTime) {
            audioElement.currentTime = element.properties.mediaStartTime;
          }
        } else if (element.type === "image") {
          // 创建图片元素
          const imageElement = document.createElement("img");
          imageElement.id = elementId;
          imageElement.src = element.properties.src;
          imageElement.crossOrigin = "anonymous";
          imageElement.style.display = "none";
          document.body.appendChild(imageElement);
        }
      }
    });
  }

  /**
   * 获取当前历史记录的操作类型
   * @returns 操作类型
   */
  getCurrentActionType(): HistoryActionType | null {
    if (
      this.currentHistoryIndex >= 0 &&
      this.currentHistoryIndex < this.history.length
    ) {
      return this.history[this.currentHistoryIndex].actionType;
    }
    return null;
  }

  /**
   * 获取可以撤销的操作类型
   * @returns 可以撤销的操作类型，如果没有则返回null
   */
  getUndoActionType(): HistoryActionType | null {
    if (this.currentHistoryIndex > 0) {
      return this.history[this.currentHistoryIndex].actionType;
    }
    return null;
  }

  /**
   * 获取可以重做的操作类型
   * @returns 可以重做的操作类型，如果没有则返回null
   */
  getRedoActionType(): HistoryActionType | null {
    if (this.currentHistoryIndex < this.history.length - 1) {
      return this.history[this.currentHistoryIndex + 1].actionType;
    }
    return null;
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.history = [];
    this.currentHistoryIndex = -1;
  }

  /**
   * 初始化历史记录，保存当前状态作为初始状态
   */
  initHistory() {
    this.clearHistory();
    this.saveToHistory("初始状态");
  }
}
