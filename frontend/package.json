{"name": "fabric-video-editor", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start-server": "ts-node src/server/index.ts"}, "browser": {"fs": false, "path": false, "os": false}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.3", "@mui/material": "^6.4.3", "@types/fabric": "^5.3.9", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.3.1", "@types/react-router-dom": "^5.3.3", "animejs": "^3.2.2", "autoprefixer": "10.4.20", "axios": "^1.7.9", "fabric": "^5.3.1", "framer-motion": "^12.7.4", "gifuct-js": "^2.1.2", "install": "^0.13.0", "lodash": "^4.17.21", "mobx": "^6.13.6", "mobx-react": "^9.2.0", "npm": "^10.8.3", "react": "18.3.1", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-dom": "18.3.1", "react-hotkeys-hook": "^4.6.1", "react-hover-video-player": "^10.0.2", "react-icons": "^5.4.0", "react-map-interaction": "^2.1.0", "react-router-dom": "^6.30.1", "react-scripts": "5.0.1", "react-virtualized": "^9.22.6", "react-virtualized-auto-sizer": "^1.0.25", "react-virtuoso": "^4.12.6", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "store": "^2.0.12", "typescript": "5.7.3"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "@types/animejs": "^3.1.13", "@types/lodash": "^4.14.202", "@types/react-color": "^3.0.13", "eslint": "9.20.0", "eslint-config-react-app": "^7.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}